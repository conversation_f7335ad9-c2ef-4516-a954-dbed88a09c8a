import logging


class HasDefault(type):
    """
    A metaclass that injects a default property into the class.

    Lazy-initializes a singleton instance.
    """

    _instance = None

    @property
    def default(cls):
        if not cls._instance:
            logging.info("Initialize default instance of %s", cls.__name__)
            cls._instance = cls()
        return cls._instance


class ServiceContext(metaclass=HasDefault):
    """
    A base class for service contexts (containing shared resources and configurations).
    They expose a `default` property that lazy-initializes a singleton instance.
    """
