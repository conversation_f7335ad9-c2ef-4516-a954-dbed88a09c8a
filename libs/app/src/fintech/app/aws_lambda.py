import json
import logging
from collections.abc import Callable
from functools import wraps
from typing import Any

from .logging import setup_logger, update_logger_lambda_context
from .retry import RetryableError
from .utils import handle_error


def lambda_handler(
    fn: Callable[[Any, Any], Any],
) -> Callable[[Any, Any], dict[str, Any]]:
    setup_logger()

    @wraps(fn)
    def wrapper(event: Any, context: Any) -> dict[str, Any]:
        update_logger_lambda_context(lambda_context=context)
        try:
            # Call the original handler function
            response_data = fn(event, context)
            return {
                "statusCode": 200,
                "body": json.dumps(response_data),
                "headers": {"Content-Type": "application/json"},
            }
        except RetryableError as e:
            logging.warning("Retryable error encountered: %s", e)
            raise
        except Exception as e:
            error_details = handle_error(e)
            raise RuntimeError(json.dumps(error_details)) from e

    return wrapper
