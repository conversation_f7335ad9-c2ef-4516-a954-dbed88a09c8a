import datetime
import json
import logging
import os
import sys


class JsonFormatterLambda(logging.Formatter):
    request_id_key = "awsRequestId"

    def __init__(self, lambda_context=None):
        self.lambda_context = lambda_context
        super().__init__()

    def format(self, record):
        relative_path = os.path.relpath(record.pathname)

        log_record = {
            "level": record.levelname,
            "timestamp": datetime.datetime.now(datetime.UTC).isoformat(),
            "file": f"{relative_path}:{record.lineno}",
            "message": record.getMessage(),
        }

        if self.lambda_context:
            log_record[JsonFormatterLambda.request_id_key] = (
                self.lambda_context.aws_request_id
            )

        return json.dumps(log_record)


def setup_logger(lambda_context=None):
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    if logger.hasHandlers():
        logger.handlers.clear()

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(JsonFormatterLambda(lambda_context))

    logger.addHandler(console_handler)

    sys.excepthook = lambda exc_type, exc_value, exc_traceback: logger.warning(
        "Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback)
    )

    return logger


def update_logger_lambda_context(lambda_context):
    for handler in logging.getLogger().handlers:
        if isinstance(handler.formatter, JsonFormatterLambda):
            handler.formatter.lambda_context = lambda_context
            break
