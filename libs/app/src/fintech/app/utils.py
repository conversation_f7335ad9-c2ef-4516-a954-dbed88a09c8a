import json
import logging
import os
import sys
import traceback
from pathlib import Path
from typing import Any

import boto3

from fintech.observability import logged


class BadRequestError(Exception):
    pass


class ExtractionError(Exception):
    pass


class ExplanationError(Exception):
    pass


def handle_error(e: Exception) -> dict[str, Any]:
    if isinstance(e, BadRequestError):
        error_code = 400
    elif isinstance(e, ExtractionError):
        error_code = 422
    elif isinstance(e, ExplanationError):
        error_code = 500
    else:
        error_code = 500

    tb = traceback.format_exc().strip().split("\n")
    logging.error("An error occurred: %s\nTraceback:\n%s", e, tb)
    additional_info = json.dumps(
        {k: str(v) for k, v in e.__dict__.items()}, default=str
    )

    if e.__traceback__ is None:
        return {
            "code": error_code,
            "message": str(e),
            "additional_info": additional_info,
            "type": type(e).__name__,
        }

    e_tb = e.__traceback__
    while (
        e_tb.tb_next is not None
        and sys.prefix not in e_tb.tb_next.tb_frame.f_code.co_filename
    ):
        e_tb = e_tb.tb_next

    return {
        "code": error_code,
        "message": (f"{e!s} in {e_tb.tb_frame.f_code.co_filename} at {e_tb.tb_lineno}"),
        "additional_info": additional_info,
        "type": type(e).__name__,
    }


def is_running_locally() -> bool:
    function_name = os.environ.get("AWS_LAMBDA_FUNCTION_NAME", "")
    return not function_name or function_name == "test_function"


@logged(service="SSM")
def load_parameters(param_path: str, with_decryption: bool = False) -> None:
    client = boto3.client("ssm", region_name="eu-central-1")

    paginator = client.get_paginator("get_parameters_by_path")
    response_iterator = paginator.paginate(
        Path=param_path, WithDecryption=with_decryption
    )

    for page in response_iterator:
        for entry in page["Parameters"]:
            name = Path(entry["Name"]).name
            value = entry["Value"]

            os.environ[name] = value

            value = os.environ.get(name)
            if value is None:
                logging.warning("env var '%s' is not set.", name)

    logging.info("Loaded parameters from SSM.")
