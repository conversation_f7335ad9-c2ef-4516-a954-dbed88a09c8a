from functools import wraps

from flask import Flask, Response, jsonify, request

from .logging import setup_logger

app = Flask(__name__)


def flask_app(route: str, methods: list[str]):
    setup_logger()

    def decorator(handler):
        @app.route(route, methods=methods)
        @wraps(handler)
        def wrapper(*args, **kwargs) -> tuple[Response, int]:
            if request.content_type == "application/json":
                data = request.get_json(silent=True)
                if data is None:
                    return jsonify({"error": "Invalid or empty JSON payload"}), 400
            else:
                data = None

            response = handler(data)
            return jsonify(response), 200

        return wrapper

    return decorator
