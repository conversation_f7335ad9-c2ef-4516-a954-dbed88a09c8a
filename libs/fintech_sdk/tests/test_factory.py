"""Tests for provider factory."""

from unittest.mock import patch

import pytest

from fintech_sdk import ProviderFactory
from fintech_sdk.config import FinTechSDKConfig
from fintech_sdk.exceptions import ConfigurationError
from fintech_sdk.interfaces import (
    AccountProvider,
    InvestmentProvider,
    PaymentProvider,
    TransactionProvider,
)


class TestProviderFactory:
    """Test provider factory functionality."""

    def test_factory_creation(self, sdk_config):
        """Test factory creation with config."""
        factory = ProviderFactory(sdk_config)
        assert factory.config == sdk_config

    def test_load_from_env(self):
        """Test loading factory from environment."""
        with patch.dict("os.environ", {"FINTECH_SDK_PROVIDER": "mock"}):
            factory = ProviderFactory.load_from_env()
            assert factory.config.provider == "mock"

    def test_load_from_config_file_not_found(self):
        """Test loading from non-existent config file."""
        with pytest.raises(ConfigurationError, match="Config file not found"):
            ProviderFactory.load_from_config("nonexistent.json")

    def test_create_account_provider(self, provider_factory):
        """Test creating account provider."""
        provider = provider_factory.create_account_provider()
        assert isinstance(provider, AccountProvider)

    def test_create_transaction_provider(self, provider_factory):
        """Test creating transaction provider."""
        provider = provider_factory.create_transaction_provider()
        assert isinstance(provider, TransactionProvider)

    def test_create_payment_provider(self, provider_factory):
        """Test creating payment provider."""
        provider = provider_factory.create_payment_provider()
        assert isinstance(provider, PaymentProvider)

    def test_create_investment_provider(self, provider_factory):
        """Test creating investment provider."""
        provider = provider_factory.create_investment_provider()
        assert isinstance(provider, InvestmentProvider)

    def test_property_access(self, provider_factory):
        """Test property-based provider access."""
        assert isinstance(provider_factory.account, AccountProvider)
        assert isinstance(provider_factory.transaction, TransactionProvider)
        assert isinstance(provider_factory.payment, PaymentProvider)
        assert isinstance(provider_factory.investment, InvestmentProvider)

    def test_list_providers(self, provider_factory):
        """Test listing available providers."""
        providers = provider_factory.list_providers()
        assert isinstance(providers, dict)
        assert "mock" in providers

        # Check that mock provider has all interfaces
        mock_interfaces = providers["mock"]
        expected_interfaces = ["account", "transaction", "payment", "investment"]
        for interface in expected_interfaces:
            assert interface in mock_interfaces

    def test_invalid_provider(self):
        """Test error handling for invalid provider."""
        config = FinTechSDKConfig(provider="nonexistent")
        factory = ProviderFactory(config)

        with pytest.raises(
            ConfigurationError, match="Provider 'nonexistent' not registered"
        ):
            factory.create_account_provider()


class TestProviderRegistry:
    """Test provider registry functionality."""

    def test_register_provider_decorator(self):
        """Test provider registration via decorator."""
        from fintech_sdk.factory import _registry, register_provider

        @register_provider("test_provider")
        class TestProvider(AccountProvider):
            async def get_account_list(self):
                return []

            async def get_account_balance(self, account_id: str):
                pass

            async def get_account(self, account_id: str):
                pass

        # Check that provider was registered
        providers = _registry.list_providers()
        assert "test_provider" in providers
        assert "account" in providers["test_provider"]

        # Check that we can get the provider class
        provider_class = _registry.get_provider("test_provider", "account")
        assert provider_class == TestProvider
