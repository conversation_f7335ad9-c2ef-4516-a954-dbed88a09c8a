"""Tests for provider implementations."""

from decimal import Decimal

import pytest

from fintech_sdk.exceptions import AccountNotFoundError, TransactionNotFoundError
from fintech_sdk.models import InvestmentOrder, InvestmentOrderType, PaymentRequest


class TestMockProvider:
    """Test mock provider implementation."""

    @pytest.mark.asyncio
    async def test_get_account_list(self, account_provider):
        """Test getting account list."""
        accounts = await account_provider.get_account_list()
        assert len(accounts) == 4
        assert accounts[0].id == "acc_checking_001"
        assert accounts[0].name == "Primary Checking"

    @pytest.mark.asyncio
    async def test_get_account_balance(self, account_provider):
        """Test getting account balance."""
        balance = await account_provider.get_account_balance("acc_checking_001")
        assert balance.account_id == "acc_checking_001"
        assert isinstance(balance.available, Decimal)
        assert isinstance(balance.current, Decimal)
        assert balance.currency == "USD"

    @pytest.mark.asyncio
    async def test_get_account_balance_not_found(self, account_provider):
        """Test getting balance for non-existent account."""
        with pytest.raises(AccountNotFoundError):
            await account_provider.get_account_balance("nonexistent")

    @pytest.mark.asyncio
    async def test_get_account(self, account_provider):
        """Test getting account details."""
        account = await account_provider.get_account("acc_checking_001")
        assert account.id == "acc_checking_001"
        assert account.name == "Primary Checking"

    @pytest.mark.asyncio
    async def test_get_transactions(self, transaction_provider):
        """Test getting transactions."""
        transactions = await transaction_provider.get_transactions("acc_checking_001")
        assert len(transactions) > 0
        assert all(txn.account_id == "acc_checking_001" for txn in transactions)

    @pytest.mark.asyncio
    async def test_get_transactions_with_pagination(self, transaction_provider):
        """Test getting transactions with pagination."""
        transactions = await transaction_provider.get_transactions(
            "acc_checking_001", limit=5, offset=0
        )
        assert len(transactions) <= 5

    @pytest.mark.asyncio
    async def test_get_transaction(self, transaction_provider):
        """Test getting specific transaction."""
        # First get a transaction ID from the list
        transactions = await transaction_provider.get_transactions("acc_checking_001")
        if transactions:
            transaction_id = transactions[0].id
            transaction = await transaction_provider.get_transaction(transaction_id)
            assert transaction.id == transaction_id

    @pytest.mark.asyncio
    async def test_get_transaction_not_found(self, transaction_provider):
        """Test getting non-existent transaction."""
        with pytest.raises(TransactionNotFoundError):
            await transaction_provider.get_transaction("nonexistent")

    @pytest.mark.asyncio
    async def test_create_payment(self, payment_provider):
        """Test creating a payment."""
        payment_request = PaymentRequest(
            to_account="acc_savings_001",
            amount=Decimal("100.00"),
            currency="USD",
            description="Test payment",
        )

        payment = await payment_provider.create_payment(
            "acc_checking_001", payment_request
        )
        assert payment.from_account == "acc_checking_001"
        assert payment.to_account == "acc_savings_001"
        assert payment.amount == Decimal("100.00")
        assert payment.currency == "USD"

    @pytest.mark.asyncio
    async def test_get_payments(self, payment_provider):
        """Test getting payments."""
        # First create a payment
        payment_request = PaymentRequest(
            to_account="acc_savings_001", amount=Decimal("50.00"), currency="USD"
        )
        await payment_provider.create_payment("acc_checking_001", payment_request)

        # Then get payments
        payments = await payment_provider.get_payments("acc_checking_001")
        assert len(payments) >= 1

    @pytest.mark.asyncio
    async def test_place_investment_order(self, investment_provider):
        """Test placing an investment order."""
        order = InvestmentOrder(
            symbol="AAPL",
            quantity=Decimal("10"),
            order_type=InvestmentOrderType.BUY,
            account_id="acc_investment_001",
        )

        investment = await investment_provider.place_order(order)
        assert investment.symbol == "AAPL"
        assert investment.quantity == Decimal("10")
        assert investment.order_type == InvestmentOrderType.BUY
        assert investment.account_id == "acc_investment_001"

    @pytest.mark.asyncio
    async def test_get_investments(self, investment_provider):
        """Test getting investments."""
        # First place an order
        order = InvestmentOrder(
            symbol="MSFT",
            quantity=Decimal("5"),
            order_type=InvestmentOrderType.BUY,
            account_id="acc_investment_001",
        )
        await investment_provider.place_order(order)

        # Then get investments
        investments = await investment_provider.get_investments("acc_investment_001")
        assert len(investments) >= 1


class TestProviderErrorHandling:
    """Test provider error handling."""

    @pytest.mark.asyncio
    async def test_mock_provider_with_errors(self, sdk_config):
        """Test mock provider with simulated errors."""
        from fintech_sdk.config import MockConfig
        from fintech_sdk.providers.mock import MockProvider

        # Configure mock provider with high error rate
        error_config = MockConfig(delay=0.0, error_rate=1.0)  # 100% error rate
        provider = MockProvider(config=error_config, sdk_config=sdk_config)

        # Should raise an exception due to simulated error
        with pytest.raises(Exception, match="Simulated provider error"):
            await provider.get_account_list()


class TestProviderIntegration:
    """Test provider integration scenarios."""

    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self, provider_factory):
        """Test complete workflow using multiple providers."""
        # Get accounts
        accounts = await provider_factory.account.get_account_list()
        assert len(accounts) > 0

        checking_account = next(
            acc for acc in accounts if acc.account_type.value == "checking"
        )

        # Get account balance
        balance = await provider_factory.account.get_account_balance(
            checking_account.id
        )
        assert balance.account_id == checking_account.id

        # Get transactions
        transactions = await provider_factory.transaction.get_transactions(
            checking_account.id, limit=5
        )
        assert len(transactions) <= 5

        # Create a payment
        payment_request = PaymentRequest(
            to_account="external_account",
            amount=Decimal("25.00"),
            currency="USD",
            description="Test payment",
        )
        payment = await provider_factory.payment.create_payment(
            checking_account.id, payment_request
        )
        assert payment.amount == Decimal("25.00")

        # Place an investment order
        investment_account = next(
            acc for acc in accounts if acc.account_type.value == "investment"
        )
        order = InvestmentOrder(
            symbol="AAPL",
            quantity=Decimal("1"),
            order_type=InvestmentOrderType.BUY,
            account_id=investment_account.id,
        )
        investment = await provider_factory.investment.place_order(order)
        assert investment.symbol == "AAPL"
