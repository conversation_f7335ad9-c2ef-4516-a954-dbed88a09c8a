#!/usr/bin/env python3
"""Basic usage example for FinTech SDK."""

import asyncio
import os
from decimal import Decimal

from fintech_sdk import ProviderFactory
from fintech_sdk.models import InvestmentOrder, InvestmentOrderType, PaymentRequest


async def main():
    """Demonstrate basic SDK usage."""
    print("🚀 FinTech SDK Basic Usage Example")
    print("=" * 40)

    # Load SDK from environment variables
    # Make sure to set FINTECH_SDK_PROVIDER=mock in your environment
    os.environ.setdefault("FINTECH_SDK_PROVIDER", "mock")

    sdk = ProviderFactory.load_from_env()
    print(f"📡 Using provider: {sdk.config.provider}")
    print()

    # 1. Account Management
    print("💰 Account Management")
    print("-" * 20)

    accounts = await sdk.account.get_account_list()
    print(f"Found {len(accounts)} accounts:")

    for account in accounts:
        print(f"  • {account.name} ({account.account_type.value})")
        print(f"    ID: {account.id}")
        print(f"    Currency: {account.currency}")

        # Get balance for each account
        balance = await sdk.account.get_account_balance(account.id)
        print(f"    Balance: {balance.current} {balance.currency}")
        print(f"    Available: {balance.available} {balance.currency}")
        print()

    # 2. Transaction History
    print("📊 Transaction History")
    print("-" * 20)

    checking_account = next(
        acc for acc in accounts if acc.account_type.value == "checking"
    )

    # Get recent transactions
    transactions = await sdk.transaction.get_transactions(checking_account.id, limit=5)

    print(f"Recent transactions for {checking_account.name}:")
    for txn in transactions:
        sign = "+" if txn.transaction_type.value == "credit" else "-"
        print(f"  {sign}{txn.amount} {txn.currency} - {txn.description}")
        print(f"    Date: {txn.date.strftime('%Y-%m-%d')}")
        print(f"    Category: {txn.category or 'N/A'}")
        print()

    # 3. Payment Processing
    print("💸 Payment Processing")
    print("-" * 20)

    # Create a payment
    payment_request = PaymentRequest(
        to_account="external_savings_account",
        amount=Decimal("150.00"),
        currency="USD",
        description="Transfer to savings",
        reference="SAVE-2024-001",
    )

    payment = await sdk.payment.create_payment(checking_account.id, payment_request)

    print("Created payment:")
    print(f"  ID: {payment.id}")
    print(f"  Amount: {payment.amount} {payment.currency}")
    print(f"  From: {payment.from_account}")
    print(f"  To: {payment.to_account}")
    print(f"  Status: {payment.status.value}")
    print(f"  Description: {payment.description}")
    print()

    # Get payment details
    payment_details = await sdk.payment.get_payment(payment.id)
    print(f"Payment status: {payment_details.status.value}")
    print()

    # 4. Investment Management
    print("📈 Investment Management")
    print("-" * 20)

    investment_account = next(
        acc for acc in accounts if acc.account_type.value == "investment"
    )

    # Place a buy order
    buy_order = InvestmentOrder(
        symbol="AAPL",
        quantity=Decimal("5"),
        order_type=InvestmentOrderType.BUY,
        account_id=investment_account.id,
        metadata={"strategy": "long_term"},
    )

    investment = await sdk.investment.place_order(buy_order)

    print("Placed investment order:")
    print(f"  ID: {investment.id}")
    print(f"  Symbol: {investment.symbol}")
    print(f"  Quantity: {investment.quantity}")
    print(f"  Type: {investment.order_type.value}")
    print(f"  Status: {investment.status.value}")
    print(f"  Price: {investment.price}")
    print()

    # Get investment portfolio
    investments = await sdk.investment.get_investments(investment_account.id)
    print(f"Investment portfolio ({len(investments)} positions):")
    for inv in investments:
        print(f"  • {inv.symbol}: {inv.quantity} shares @ {inv.price}")
        print(f"    Status: {inv.status.value}")
    print()

    # 5. Error Handling Example
    print("⚠️  Error Handling")
    print("-" * 20)

    try:
        # Try to get a non-existent account
        await sdk.account.get_account("nonexistent_account")
    except Exception as e:
        print(f"Caught expected error: {type(e).__name__}: {e}")

    print()
    print("✅ Example completed successfully!")


if __name__ == "__main__":
    asyncio.run(main())
