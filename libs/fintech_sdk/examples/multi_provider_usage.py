#!/usr/bin/env python3
"""Multi-provider usage example for FinTech SDK."""

import asyncio
import os

from fintech_sdk import ProviderFactory
from fintech_sdk.config import FinTechSDKConfig


async def demonstrate_regional_providers():
    """Demonstrate using different providers for different regions."""
    print("🌍 Multi-Provider Regional Example")
    print("=" * 50)

    # Set up regional provider configuration
    os.environ.update(
        {
            "FINTECH_SDK_PROVIDER": "mock",  # Default fallback
            "FINTECH_SDK_PROVIDER_SWITZERLAND": "swiss_bank",
            "FINTECH_SDK_PROVIDER_GERMANY": "german_bank",
        }
    )

    # 1. Switzerland Operations
    print("🇨🇭 Switzerland Operations (Swiss Bank)")
    print("-" * 30)

    swiss_sdk = ProviderFactory.load_from_env(region="switzerland")
    print(f"Selected provider: {swiss_sdk._get_effective_provider_name()}")

    swiss_accounts = await swiss_sdk.account.get_account_list()
    print(f"Found {len(swiss_accounts)} Swiss accounts:")

    for account in swiss_accounts:
        print(f"  • {account.name}")
        print(f"    Currency: {account.currency}")
        print(f"    IBAN: {account.metadata.get('iban', 'N/A')}")

        balance = await swiss_sdk.account.get_account_balance(account.id)
        print(f"    Balance: {balance.current} {balance.currency}")
        print()

    # Get Swiss transactions
    swiss_checking = next(
        acc for acc in swiss_accounts if acc.account_type.value == "checking"
    )
    swiss_transactions = await swiss_sdk.transaction.get_transactions(swiss_checking.id)

    print("Recent Swiss transactions:")
    for txn in swiss_transactions[:3]:
        print(f"  • {txn.description}: {txn.amount} {txn.currency}")
    print()

    # 2. Germany Operations
    print("🇩🇪 Germany Operations (German Bank)")
    print("-" * 30)

    german_sdk = ProviderFactory.load_from_env(region="germany")
    print(f"Selected provider: {german_sdk._get_effective_provider_name()}")

    german_accounts = await german_sdk.account.get_account_list()
    print(f"Found {len(german_accounts)} German accounts:")

    for account in german_accounts:
        print(f"  • {account.name}")
        print(f"    Currency: {account.currency}")
        print(f"    IBAN: {account.metadata.get('iban', 'N/A')}")

        balance = await german_sdk.account.get_account_balance(account.id)
        print(f"    Balance: {balance.current} {balance.currency}")
        print()

    # Get German transactions
    german_checking = next(
        acc for acc in german_accounts if acc.account_type.value == "checking"
    )
    german_transactions = await german_sdk.transaction.get_transactions(
        german_checking.id
    )

    print("Recent German transactions:")
    for txn in german_transactions[:3]:
        print(f"  • {txn.description}: {txn.amount} {txn.currency}")
    print()

    # 3. Default Operations (Mock Provider)
    print("🌐 Default Operations (Mock Provider)")
    print("-" * 30)

    default_sdk = ProviderFactory.load_from_env()  # No region specified
    print(f"Selected provider: {default_sdk._get_effective_provider_name()}")

    default_accounts = await default_sdk.account.get_account_list()
    print(f"Found {len(default_accounts)} default accounts:")

    for account in default_accounts[:2]:  # Show first 2 accounts
        print(f"  • {account.name}")
        print(f"    Currency: {account.currency}")

        balance = await default_sdk.account.get_account_balance(account.id)
        print(f"    Balance: {balance.current} {balance.currency}")
        print()


async def demonstrate_provider_switching():
    """Demonstrate switching providers programmatically."""
    print("🔄 Provider Switching Example")
    print("=" * 40)

    base_config = FinTechSDKConfig(provider="mock", timeout=30)

    # Test different providers
    providers_to_test = [
        ("mock", None),
        ("swiss_bank", "switzerland"),
        ("german_bank", "germany"),
    ]

    for provider_name, region in providers_to_test:
        print(
            f"Testing {provider_name} provider"
            + (f" (region: {region})" if region else "")
        )

        if region:
            # Use regional override
            os.environ[f"FINTECH_SDK_PROVIDER_{region.upper()}"] = provider_name
            factory = ProviderFactory(base_config, region=region)
        else:
            # Use direct provider
            config = FinTechSDKConfig(provider=provider_name)
            factory = ProviderFactory(config)

        try:
            accounts = await factory.account.get_account_list()
            print(f"  ✅ Success: {len(accounts)} accounts found")

            if accounts:
                first_account = accounts[0]
                print(
                    f"     First account: {first_account.name} ({first_account.currency})"
                )

                balance = await factory.account.get_account_balance(first_account.id)
                print(f"     Balance: {balance.current} {balance.currency}")

        except Exception as e:
            print(f"  ❌ Error: {e}")

        print()


async def demonstrate_configuration_scenarios():
    """Demonstrate different configuration scenarios."""
    print("⚙️  Configuration Scenarios")
    print("=" * 40)

    # Scenario 1: Environment-based configuration
    print("1. Environment-based configuration:")
    os.environ.update(
        {
            "FINTECH_SDK_PROVIDER": "mock",
            "FINTECH_SDK_TIMEOUT": "45",
            "FINTECH_SDK_ENVIRONMENT": "sandbox",
        }
    )

    env_sdk = ProviderFactory.load_from_env()
    print(f"   Provider: {env_sdk.config.provider}")
    print(f"   Timeout: {env_sdk.config.timeout}s")
    print(f"   Environment: {env_sdk.config.environment}")
    print()

    # Scenario 2: Programmatic configuration
    print("2. Programmatic configuration:")
    prog_config = FinTechSDKConfig(
        provider="swiss_bank", timeout=60, environment="production"
    )
    prog_sdk = ProviderFactory(prog_config)
    print(f"   Provider: {prog_sdk.config.provider}")
    print(f"   Timeout: {prog_sdk.config.timeout}s")
    print(f"   Environment: {prog_sdk.config.environment}")
    print()

    # Scenario 3: Regional override
    print("3. Regional override:")
    os.environ["FINTECH_SDK_PROVIDER_ITALY"] = "mock"  # Fallback for Italy

    italy_sdk = ProviderFactory.load_from_env(region="italy")
    print("   Region: italy")
    print(f"   Effective provider: {italy_sdk._get_effective_provider_name()}")
    print()


async def demonstrate_error_handling():
    """Demonstrate error handling with different providers."""
    print("🚨 Error Handling Example")
    print("=" * 30)

    # Test with Swiss provider
    swiss_sdk = ProviderFactory(FinTechSDKConfig(provider="swiss_bank"))

    try:
        # Try to get a non-existent account
        await swiss_sdk.account.get_account("nonexistent_swiss_account")
    except Exception as e:
        print(f"Swiss provider error: {type(e).__name__}: {e}")

    try:
        # Try to get a non-existent transaction
        await swiss_sdk.transaction.get_transaction("nonexistent_swiss_txn")
    except Exception as e:
        print(f"Swiss provider error: {type(e).__name__}: {e}")

    print()


async def main():
    """Run all examples."""
    await demonstrate_regional_providers()
    await demonstrate_provider_switching()
    await demonstrate_configuration_scenarios()
    await demonstrate_error_handling()

    print("✅ Multi-provider examples completed successfully!")


if __name__ == "__main__":
    asyncio.run(main())
