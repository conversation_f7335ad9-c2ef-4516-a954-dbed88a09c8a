"""German Bank provider implementation for Germany region."""

from datetime import datetime
from decimal import Decimal

from ..exceptions import AccountNotFoundError, TransactionNotFoundError
from ..factory import register_provider
from ..interfaces import AccountProvider, TransactionProvider
from ..models import Account, AccountBalance, AccountType, Transaction, TransactionType
from .base import BaseProvider


@register_provider("german_bank")
class GermanBankProvider(BaseProvider, AccountProvider, TransactionProvider):
    """German Bank provider for Germany region."""

    def __init__(self, config=None, **kwargs):
        super().__init__(config, **kwargs)
        self.base_url = "https://api.deutschebank.de/v1"

    async def get_account_list(self) -> list[Account]:
        """Get list of accounts from German Bank."""
        # Mock German Bank accounts with EUR currency
        mock_accounts = [
            {
                "account_id": "de_checking_001",
                "name": "German Checking Account",
                "type": "checking",
                "currency": "EUR",
                "iban": "DE89 3704 0044 0532 0130 00",
            },
            {
                "account_id": "de_savings_001",
                "name": "German Savings Account",
                "type": "savings",
                "currency": "EUR",
                "iban": "DE75 5121 0800 1245 1261 31",
            },
        ]

        accounts = []
        for acc_data in mock_accounts:
            account_type = (
                AccountType.CHECKING
                if acc_data["type"] == "checking"
                else AccountType.SAVINGS
            )

            account = Account(
                id=acc_data["account_id"],
                name=acc_data["name"],
                account_type=account_type,
                currency=acc_data["currency"],
                provider_id=acc_data["account_id"],
                metadata={
                    "iban": acc_data["iban"],
                    "bank": "Deutsche Bank",
                    "country": "Germany",
                },
            )
            accounts.append(account)

        return accounts

    async def get_account_balance(self, account_id: str) -> AccountBalance:
        """Get account balance from German Bank."""
        if account_id == "de_checking_001":
            return AccountBalance(
                account_id=account_id,
                available=Decimal("3500.25"),
                current=Decimal("3750.50"),
                currency="EUR",
                last_updated=datetime.now(),
            )
        elif account_id == "de_savings_001":
            return AccountBalance(
                account_id=account_id,
                available=Decimal("18000.00"),
                current=Decimal("18000.00"),
                currency="EUR",
                last_updated=datetime.now(),
            )
        else:
            raise AccountNotFoundError(
                f"Account {account_id} not found", provider="german_bank"
            )

    async def get_account(self, account_id: str) -> Account:
        """Get account details from German Bank."""
        accounts = await self.get_account_list()
        account = next((acc for acc in accounts if acc.id == account_id), None)

        if not account:
            raise AccountNotFoundError(
                f"Account {account_id} not found", provider="german_bank"
            )

        return account

    async def get_transactions(
        self,
        account_id: str,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        limit: int | None = None,
        offset: int | None = None,
    ) -> list[Transaction]:
        """Get transactions from German Bank."""
        # Mock German transactions
        mock_transactions = [
            {
                "transaction_id": "de_txn_001",
                "account_id": account_id,
                "amount": -35.90,
                "currency": "EUR",
                "date": "2024-01-15",
                "description": "REWE Supermarket",
                "category": "Groceries",
            },
            {
                "transaction_id": "de_txn_002",
                "account_id": account_id,
                "amount": -89.50,
                "currency": "EUR",
                "date": "2024-01-14",
                "description": "Deutsche Bahn",
                "category": "Transportation",
            },
        ]

        transactions = []
        for txn_data in mock_transactions:
            transaction = Transaction(
                id=txn_data["transaction_id"],
                account_id=txn_data["account_id"],
                amount=Decimal(str(abs(txn_data["amount"]))),
                currency=txn_data["currency"],
                transaction_type=(
                    TransactionType.DEBIT
                    if txn_data["amount"] < 0
                    else TransactionType.CREDIT
                ),
                description=txn_data["description"],
                date=datetime.fromisoformat(txn_data["date"]),
                category=txn_data["category"],
                merchant=txn_data["description"],
                provider_id=txn_data["transaction_id"],
                metadata={"country": "Germany", "original_amount": txn_data["amount"]},
            )
            transactions.append(transaction)

        return transactions

    async def get_transaction(self, transaction_id: str) -> Transaction:
        """Get a specific transaction from German Bank."""
        if transaction_id.startswith("de_txn_"):
            return Transaction(
                id=transaction_id,
                account_id="de_checking_001",
                amount=Decimal("35.90"),
                currency="EUR",
                transaction_type=TransactionType.DEBIT,
                description="German Bank Transaction",
                date=datetime.now(),
                category="general",
                merchant="German Merchant",
                provider_id=transaction_id,
                metadata={"provider": "german_bank", "country": "Germany"},
            )
        else:
            raise TransactionNotFoundError(
                f"Transaction {transaction_id} not found", provider="german_bank"
            )
