"""Tink provider implementation."""

from datetime import datetime
from decimal import Decimal
from uuid import uuid4

from ..config import TinkConfig
from ..exceptions import InvestmentNotFoundError
from ..factory import register_provider
from ..interfaces import InvestmentProvider
from ..models import (
    Investment,
    InvestmentOrder,
    InvestmentOrderStatus,
    InvestmentOrderType,
)
from .base import BaseProvider


@register_provider("tink")
class TinkProvider(BaseProvider, InvestmentProvider):
    """Tink provider for investment services."""

    def __init__(self, config: TinkConfig, **kwargs):
        super().__init__(config, **kwargs)
        self.base_url = config.base_url or self._get_base_url(config.environment)
        self._access_token = None

    def _get_base_url(self, environment: str) -> str:
        """Get Tink base URL for environment."""
        urls = {
            "sandbox": "https://api.tink.com/api/v1",
            "production": "https://api.tink.com/api/v1",
        }
        return urls.get(environment, urls["sandbox"])

    async def _get_access_token(self) -> str:
        """Get OAuth access token from Tink."""
        if self._access_token:
            return self._access_token

        # In a real implementation, this would authenticate with Tink
        # response = await self._make_request(
        #     "POST",
        #     f"{self.base_url}/oauth/token",
        #     headers={"Content-Type": "application/x-www-form-urlencoded"},
        #     data={
        #         "client_id": self.config.client_id,
        #         "client_secret": self.config.client_secret,
        #         "grant_type": "client_credentials",
        #         "scope": "investments:read investments:write"
        #     }
        # )
        # self._access_token = response["access_token"]

        # Mock access token
        self._access_token = "mock_tink_access_token"
        return self._access_token

    def _get_auth_headers(self) -> dict:
        """Get authentication headers."""
        headers = self._get_default_headers()
        # In async context, we'd need to await this
        # For now, use a mock token
        headers.update(
            {
                "Authorization": "Bearer mock_tink_access_token",
            }
        )
        return headers

    async def place_order(self, order: InvestmentOrder) -> Investment:
        """Place an investment order via Tink."""
        # In a real implementation, this would place an order through Tink
        # response = await self._make_request(
        #     "POST",
        #     f"{self.base_url}/investments/orders",
        #     headers=self._get_auth_headers(),
        #     json={
        #         "account_id": order.account_id,
        #         "instrument": order.symbol,
        #         "side": "buy" if order.order_type == InvestmentOrderType.BUY else "sell",
        #         "quantity": str(order.quantity),
        #         "price": str(order.price) if order.price else None,
        #         "order_type": "market" if not order.price else "limit"
        #     }
        # )

        # Mock Tink order response
        investment_id = f"tink_order_{uuid4().hex[:12]}"

        investment = Investment(
            id=investment_id,
            symbol=order.symbol,
            quantity=order.quantity,
            order_type=order.order_type,
            status=InvestmentOrderStatus.PENDING,
            account_id=order.account_id,
            price=order.price or Decimal("150.00"),  # Mock market price
            created_at=datetime.now(),
            updated_at=datetime.now(),
            provider_id=investment_id,
            metadata={
                **order.metadata,
                "tink_order_id": investment_id,
                "tink_status": "pending",
                "market": (
                    "NASDAQ" if order.symbol in ["AAPL", "MSFT", "GOOGL"] else "NYSE"
                ),
            },
        )

        return investment

    async def get_investment(self, investment_id: str) -> Investment:
        """Get investment details from Tink."""
        # In a real implementation, this would fetch the order details
        # response = await self._make_request(
        #     "GET",
        #     f"{self.base_url}/investments/orders/{investment_id}",
        #     headers=self._get_auth_headers()
        # )

        # Mock response
        if investment_id.startswith("tink_order_"):
            return Investment(
                id=investment_id,
                symbol="AAPL",
                quantity=Decimal("10"),
                order_type=InvestmentOrderType.BUY,
                status=InvestmentOrderStatus.FILLED,
                account_id="tink_investment_001",
                price=Decimal("150.00"),
                created_at=datetime.now(),
                updated_at=datetime.now(),
                provider_id=investment_id,
                metadata={
                    "tink_order_id": investment_id,
                    "tink_status": "filled",
                    "market": "NASDAQ",
                    "execution_price": "150.00",
                },
            )
        else:
            raise InvestmentNotFoundError(
                f"Investment {investment_id} not found", provider="tink"
            )

    async def get_investments(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[Investment]:
        """Get investments from Tink."""
        # In a real implementation, this would list orders for the account
        # response = await self._make_request(
        #     "GET",
        #     f"{self.base_url}/investments/orders",
        #     headers=self._get_auth_headers(),
        #     params={
        #         "account_id": account_id,
        #         "limit": limit,
        #         "offset": offset
        #     }
        # )

        # Mock investments
        mock_investments = []
        symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"]

        for i in range(min(limit, 5)):  # Return up to 5 mock investments
            investment_id = f"tink_order_{uuid4().hex[:12]}"
            symbol = symbols[i % len(symbols)]

            investment = Investment(
                id=investment_id,
                symbol=symbol,
                quantity=Decimal(f"{10 + i * 5}"),
                order_type=(
                    InvestmentOrderType.BUY if i % 2 == 0 else InvestmentOrderType.SELL
                ),
                status=InvestmentOrderStatus.FILLED,
                account_id=account_id,
                price=Decimal(f"{100 + i * 25}.00"),
                created_at=datetime.now(),
                updated_at=datetime.now(),
                provider_id=investment_id,
                metadata={
                    "tink_order_id": investment_id,
                    "tink_status": "filled",
                    "market": (
                        "NASDAQ" if symbol in ["AAPL", "MSFT", "GOOGL"] else "NYSE"
                    ),
                },
            )
            mock_investments.append(investment)

        return mock_investments

    async def cancel_order(self, investment_id: str) -> Investment:
        """Cancel an investment order in Tink."""
        # In a real implementation, this would cancel the order
        # response = await self._make_request(
        #     "DELETE",
        #     f"{self.base_url}/investments/orders/{investment_id}",
        #     headers=self._get_auth_headers()
        # )

        # Mock cancellation
        if investment_id.startswith("tink_order_"):
            investment = Investment(
                id=investment_id,
                symbol="AAPL",
                quantity=Decimal("10"),
                order_type=InvestmentOrderType.BUY,
                status=InvestmentOrderStatus.CANCELLED,
                account_id="tink_investment_001",
                price=Decimal("150.00"),
                created_at=datetime.now(),
                updated_at=datetime.now(),
                provider_id=investment_id,
                metadata={
                    "tink_order_id": investment_id,
                    "tink_status": "cancelled",
                    "market": "NASDAQ",
                },
            )
            return investment
        else:
            raise InvestmentNotFoundError(
                f"Investment {investment_id} not found", provider="tink"
            )
