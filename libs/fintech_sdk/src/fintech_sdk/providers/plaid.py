"""Plaid provider implementation."""

from datetime import datetime
from decimal import Decimal

from ..config import PlaidConfig
from ..exceptions import AccountNotFoundError, TransactionNotFoundError
from ..factory import register_provider
from ..interfaces import AccountProvider, TransactionProvider
from ..models import Account, AccountBalance, AccountType, Transaction, TransactionType
from .base import BaseProvider


@register_provider("plaid")
class PlaidProvider(BaseProvider, AccountProvider, TransactionProvider):
    """Plaid provider for account and transaction data."""

    def __init__(self, config: PlaidConfig, **kwargs):
        super().__init__(config, **kwargs)
        self.base_url = config.base_url or self._get_base_url(config.environment)

    def _get_base_url(self, environment: str) -> str:
        """Get Plaid base URL for environment."""
        urls = {
            "sandbox": "https://sandbox.plaid.com",
            "development": "https://development.plaid.com",
            "production": "https://production.plaid.com",
        }
        return urls.get(environment, urls["sandbox"])

    def _get_auth_headers(self) -> dict:
        """Get authentication headers."""
        headers = self._get_default_headers()
        headers.update(
            {
                "PLAID-CLIENT-ID": self.config.client_id,
                "PLAID-SECRET": self.config.secret,
            }
        )
        return headers

    async def get_account_list(self) -> list[Account]:
        """Get list of accounts from Plaid."""
        # In a real implementation, this would make an API call to Plaid
        # For now, return mock data that follows Plaid's structure

        # Mock Plaid API call
        # response = await self._make_request(
        #     "POST",
        #     f"{self.base_url}/accounts/get",
        #     headers=self._get_auth_headers(),
        #     json={"access_token": "your_access_token"}
        # )

        # Mock response data
        mock_accounts = [
            {
                "account_id": "plaid_checking_001",
                "name": "Plaid Checking",
                "type": "depository",
                "subtype": "checking",
                "balances": {
                    "available": 1000.50,
                    "current": 1200.75,
                    "iso_currency_code": "USD",
                },
            },
            {
                "account_id": "plaid_savings_001",
                "name": "Plaid Savings",
                "type": "depository",
                "subtype": "savings",
                "balances": {
                    "available": 5000.00,
                    "current": 5000.00,
                    "iso_currency_code": "USD",
                },
            },
        ]

        accounts = []
        for acc_data in mock_accounts:
            account_type = (
                AccountType.CHECKING
                if acc_data["subtype"] == "checking"
                else AccountType.SAVINGS
            )

            account = Account(
                id=acc_data["account_id"],
                name=acc_data["name"],
                account_type=account_type,
                currency=acc_data["balances"]["iso_currency_code"],
                provider_id=acc_data["account_id"],
                metadata={
                    "plaid_type": acc_data["type"],
                    "plaid_subtype": acc_data["subtype"],
                },
            )
            accounts.append(account)

        return accounts

    async def get_account_balance(self, account_id: str) -> AccountBalance:
        """Get account balance from Plaid."""
        # Mock Plaid API call
        # response = await self._make_request(
        #     "POST",
        #     f"{self.base_url}/accounts/balance/get",
        #     headers=self._get_auth_headers(),
        #     json={
        #         "access_token": "your_access_token",
        #         "account_ids": [account_id]
        #     }
        # )

        # Mock response
        if account_id == "plaid_checking_001":
            return AccountBalance(
                account_id=account_id,
                available=Decimal("1000.50"),
                current=Decimal("1200.75"),
                currency="USD",
                last_updated=datetime.now(),
            )
        elif account_id == "plaid_savings_001":
            return AccountBalance(
                account_id=account_id,
                available=Decimal("5000.00"),
                current=Decimal("5000.00"),
                currency="USD",
                last_updated=datetime.now(),
            )
        else:
            raise AccountNotFoundError(
                f"Account {account_id} not found", provider="plaid"
            )

    async def get_account(self, account_id: str) -> Account:
        """Get account details from Plaid."""
        accounts = await self.get_account_list()
        account = next((acc for acc in accounts if acc.id == account_id), None)

        if not account:
            raise AccountNotFoundError(
                f"Account {account_id} not found", provider="plaid"
            )

        return account

    async def get_transactions(
        self,
        account_id: str,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        limit: int | None = None,
        offset: int | None = None,
    ) -> list[Transaction]:
        """Get transactions from Plaid."""
        # Mock Plaid API call
        # response = await self._make_request(
        #     "POST",
        #     f"{self.base_url}/transactions/get",
        #     headers=self._get_auth_headers(),
        #     json={
        #         "access_token": "your_access_token",
        #         "start_date": start_date.date().isoformat() if start_date else None,
        #         "end_date": end_date.date().isoformat() if end_date else None,
        #         "account_ids": [account_id],
        #         "count": limit or 100,
        #         "offset": offset or 0
        #     }
        # )

        # Mock transactions
        mock_transactions = [
            {
                "transaction_id": "plaid_txn_001",
                "account_id": account_id,
                "amount": -25.50,
                "iso_currency_code": "USD",
                "date": "2024-01-15",
                "name": "Starbucks Coffee",
                "merchant_name": "Starbucks",
                "category": ["Food and Drink", "Restaurants", "Coffee Shop"],
            },
            {
                "transaction_id": "plaid_txn_002",
                "account_id": account_id,
                "amount": -75.00,
                "iso_currency_code": "USD",
                "date": "2024-01-14",
                "name": "Shell Gas Station",
                "merchant_name": "Shell",
                "category": ["Transportation", "Gas Stations"],
            },
        ]

        transactions = []
        for txn_data in mock_transactions:
            transaction = Transaction(
                id=txn_data["transaction_id"],
                account_id=txn_data["account_id"],
                amount=Decimal(str(abs(txn_data["amount"]))),
                currency=txn_data["iso_currency_code"],
                transaction_type=(
                    TransactionType.DEBIT
                    if txn_data["amount"] < 0
                    else TransactionType.CREDIT
                ),
                description=txn_data["name"],
                date=datetime.fromisoformat(txn_data["date"]),
                category=txn_data["category"][0] if txn_data["category"] else None,
                merchant=txn_data.get("merchant_name"),
                provider_id=txn_data["transaction_id"],
                metadata={
                    "plaid_categories": txn_data["category"],
                    "plaid_amount": txn_data["amount"],
                },
            )
            transactions.append(transaction)

        return transactions

    async def get_transaction(self, transaction_id: str) -> Transaction:
        """Get a specific transaction from Plaid."""
        # In a real implementation, this would fetch the specific transaction
        # For now, return a mock transaction

        if transaction_id.startswith("plaid_txn_"):
            return Transaction(
                id=transaction_id,
                account_id="plaid_checking_001",
                amount=Decimal("25.50"),
                currency="USD",
                transaction_type=TransactionType.DEBIT,
                description="Mock Plaid Transaction",
                date=datetime.now(),
                category="food",
                merchant="Mock Merchant",
                provider_id=transaction_id,
                metadata={"provider": "plaid"},
            )
        else:
            raise TransactionNotFoundError(
                f"Transaction {transaction_id} not found", provider="plaid"
            )
