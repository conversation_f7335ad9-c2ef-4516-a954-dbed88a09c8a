"""Mock provider implementation for testing."""

import asyncio
import random
from datetime import datetime, timedelta
from decimal import Decimal
from uuid import uuid4

from ..config import MockConfig
from ..exceptions import (
    AccountNotFoundError,
    InvestmentNotFoundError,
    PaymentNotFoundError,
    TransactionNotFoundError,
)
from ..factory import register_provider
from ..interfaces import (
    AccountProvider,
    InvestmentProvider,
    PaymentProvider,
    TransactionProvider,
)
from ..models import (
    Account,
    AccountBalance,
    AccountType,
    Investment,
    InvestmentOrder,
    InvestmentOrderStatus,
    Payment,
    PaymentRequest,
    PaymentStatus,
    Transaction,
    TransactionType,
)
from .base import BaseProvider


@register_provider("mock")
class MockProvider(
    BaseProvider,
    AccountProvider,
    TransactionProvider,
    PaymentProvider,
    InvestmentProvider,
):
    """Mock provider for testing and development."""

    # Class-level storage to persist across instances
    _class_accounts = None
    _class_transactions = None
    _class_payments = {}
    _class_investments = {}

    def __init__(self, config: MockConfig = None, **kwargs):
        super().__init__(config or MockConfig(), **kwargs)

        # Initialize class-level storage if not already done
        if MockProvider._class_accounts is None:
            MockProvider._class_accounts = self._generate_mock_accounts()
            MockProvider._class_transactions = self._generate_mock_transactions()

        # Use class-level storage
        self._accounts = MockProvider._class_accounts
        self._transactions = MockProvider._class_transactions
        self._payments = MockProvider._class_payments
        self._investments = MockProvider._class_investments

    def _generate_mock_accounts(self) -> list[Account]:
        """Generate mock accounts."""
        return [
            Account(
                id="acc_checking_001",
                name="Primary Checking",
                account_type=AccountType.CHECKING,
                currency="USD",
                provider_id="mock_checking_001",
                metadata={"bank": "Mock Bank", "routing": "*********"},
            ),
            Account(
                id="acc_savings_001",
                name="High Yield Savings",
                account_type=AccountType.SAVINGS,
                currency="USD",
                provider_id="mock_savings_001",
                metadata={"bank": "Mock Bank", "apy": "2.5%"},
            ),
            Account(
                id="acc_credit_001",
                name="Rewards Credit Card",
                account_type=AccountType.CREDIT,
                currency="USD",
                provider_id="mock_credit_001",
                metadata={"bank": "Mock Bank", "limit": "5000.00"},
            ),
            Account(
                id="acc_investment_001",
                name="Investment Account",
                account_type=AccountType.INVESTMENT,
                currency="USD",
                provider_id="mock_investment_001",
                metadata={"broker": "Mock Broker", "type": "taxable"},
            ),
        ]

    def _generate_mock_transactions(self) -> list[Transaction]:
        """Generate mock transactions."""
        transactions = []
        base_date = datetime.now() - timedelta(days=30)

        for i in range(20):
            transaction_date = base_date + timedelta(days=i)
            transactions.append(
                Transaction(
                    id=f"txn_{i:03d}",
                    account_id="acc_checking_001",
                    amount=Decimal(str(random.uniform(-500, 200))),
                    currency="USD",
                    transaction_type=(
                        TransactionType.DEBIT
                        if random.random() > 0.3
                        else TransactionType.CREDIT
                    ),
                    description=random.choice(
                        [
                            "Grocery Store Purchase",
                            "Gas Station",
                            "Online Transfer",
                            "ATM Withdrawal",
                            "Direct Deposit",
                            "Restaurant",
                            "Subscription Service",
                        ]
                    ),
                    date=transaction_date,
                    category=random.choice(
                        ["food", "transport", "entertainment", "utilities", "income"]
                    ),
                    merchant=f"Mock Merchant {i}",
                    provider_id=f"mock_txn_{i:03d}",
                    metadata={"location": "Mock City, ST"},
                )
            )

        return transactions

    async def _simulate_delay_and_errors(self):
        """Simulate network delay and random errors."""
        if self.config.delay > 0:
            await asyncio.sleep(self.config.delay)

        if self.config.error_rate > 0 and random.random() < self.config.error_rate:
            raise Exception("Simulated provider error")

    # Account Provider Implementation

    async def get_account_list(self) -> list[Account]:
        """Get list of accounts."""
        await self._simulate_delay_and_errors()
        return self._accounts.copy()

    async def get_account_balance(self, account_id: str) -> AccountBalance:
        """Get account balance."""
        await self._simulate_delay_and_errors()

        account = next((acc for acc in self._accounts if acc.id == account_id), None)
        if not account:
            raise AccountNotFoundError(
                f"Account {account_id} not found", provider="mock"
            )

        # Generate realistic balances based on account type
        if account.account_type == AccountType.CHECKING:
            current = Decimal("2500.75")
            available = Decimal("2450.75")
        elif account.account_type == AccountType.SAVINGS:
            current = Decimal("15000.00")
            available = current
        elif account.account_type == AccountType.CREDIT:
            current = Decimal("-850.25")  # Negative for credit
            available = Decimal("4149.75")  # Available credit
        else:  # Investment
            current = Decimal("25000.50")
            available = Decimal("1000.00")  # Cash available

        return AccountBalance(
            account_id=account_id,
            available=available,
            current=current,
            currency=account.currency,
            last_updated=datetime.now(),
        )

    async def get_account(self, account_id: str) -> Account:
        """Get account details."""
        await self._simulate_delay_and_errors()

        account = next((acc for acc in self._accounts if acc.id == account_id), None)
        if not account:
            raise AccountNotFoundError(
                f"Account {account_id} not found", provider="mock"
            )

        return account

    # Transaction Provider Implementation

    async def get_transactions(
        self,
        account_id: str,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        limit: int | None = None,
        offset: int | None = None,
    ) -> list[Transaction]:
        """Get transactions for an account."""
        await self._simulate_delay_and_errors()

        # Filter transactions by account
        transactions = [
            txn for txn in self._transactions if txn.account_id == account_id
        ]

        # Apply date filters
        if start_date:
            transactions = [txn for txn in transactions if txn.date >= start_date]
        if end_date:
            transactions = [txn for txn in transactions if txn.date <= end_date]

        # Sort by date (newest first)
        transactions.sort(key=lambda x: x.date, reverse=True)

        # Apply pagination
        if offset:
            transactions = transactions[offset:]
        if limit:
            transactions = transactions[:limit]

        return transactions

    async def get_transaction(self, transaction_id: str) -> Transaction:
        """Get a specific transaction."""
        await self._simulate_delay_and_errors()

        transaction = next(
            (txn for txn in self._transactions if txn.id == transaction_id), None
        )
        if not transaction:
            raise TransactionNotFoundError(
                f"Transaction {transaction_id} not found", provider="mock"
            )

        return transaction

    # Payment Provider Implementation

    async def create_payment(
        self, from_account: str, payment_request: PaymentRequest
    ) -> Payment:
        """Create a payment."""
        await self._simulate_delay_and_errors()

        payment_id = f"pay_{uuid4().hex[:8]}"
        payment = Payment(
            id=payment_id,
            from_account=from_account,
            to_account=payment_request.to_account,
            amount=payment_request.amount,
            currency=payment_request.currency,
            status=PaymentStatus.PENDING,
            description=payment_request.description,
            reference=payment_request.reference,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            provider_id=f"mock_pay_{payment_id}",
            metadata=payment_request.metadata,
        )

        self._payments[payment_id] = payment
        return payment

    async def get_payment(self, payment_id: str) -> Payment:
        """Get payment details."""
        await self._simulate_delay_and_errors()

        if payment_id not in self._payments:
            raise PaymentNotFoundError(
                f"Payment {payment_id} not found", provider="mock"
            )

        return self._payments[payment_id]

    async def get_payments(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[Payment]:
        """Get payments for an account."""
        await self._simulate_delay_and_errors()

        payments = [
            payment
            for payment in self._payments.values()
            if payment.from_account == account_id or payment.to_account == account_id
        ]

        # Sort by creation date (newest first)
        payments.sort(key=lambda x: x.created_at, reverse=True)

        # Apply pagination
        return payments[offset : offset + limit]

    async def cancel_payment(self, payment_id: str) -> Payment:
        """Cancel a payment."""
        await self._simulate_delay_and_errors()

        if payment_id not in self._payments:
            raise PaymentNotFoundError(
                f"Payment {payment_id} not found", provider="mock"
            )

        payment = self._payments[payment_id]
        if payment.status not in [PaymentStatus.PENDING, PaymentStatus.PROCESSING]:
            raise Exception(f"Payment {payment_id} cannot be cancelled")

        payment.status = PaymentStatus.CANCELLED
        payment.updated_at = datetime.now()

        return payment

    # Investment Provider Implementation

    async def place_order(self, order: InvestmentOrder) -> Investment:
        """Place an investment order."""
        await self._simulate_delay_and_errors()

        investment_id = f"inv_{uuid4().hex[:8]}"
        investment = Investment(
            id=investment_id,
            symbol=order.symbol,
            quantity=order.quantity,
            order_type=order.order_type,
            status=InvestmentOrderStatus.PENDING,
            account_id=order.account_id,
            price=order.price or Decimal("100.00"),  # Mock price
            created_at=datetime.now(),
            updated_at=datetime.now(),
            provider_id=f"mock_inv_{investment_id}",
            metadata=order.metadata,
        )

        self._investments[investment_id] = investment
        return investment

    async def get_investment(self, investment_id: str) -> Investment:
        """Get investment details."""
        await self._simulate_delay_and_errors()

        if investment_id not in self._investments:
            raise InvestmentNotFoundError(
                f"Investment {investment_id} not found", provider="mock"
            )

        return self._investments[investment_id]

    async def get_investments(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[Investment]:
        """Get investments for an account."""
        await self._simulate_delay_and_errors()

        investments = [
            investment
            for investment in self._investments.values()
            if investment.account_id == account_id
        ]

        # Sort by creation date (newest first)
        investments.sort(key=lambda x: x.created_at, reverse=True)

        # Apply pagination
        return investments[offset : offset + limit]

    async def cancel_order(self, investment_id: str) -> Investment:
        """Cancel an investment order."""
        await self._simulate_delay_and_errors()

        if investment_id not in self._investments:
            raise InvestmentNotFoundError(
                f"Investment {investment_id} not found", provider="mock"
            )

        investment = self._investments[investment_id]
        if investment.status not in [InvestmentOrderStatus.PENDING]:
            raise Exception(f"Investment order {investment_id} cannot be cancelled")

        investment.status = InvestmentOrderStatus.CANCELLED
        investment.updated_at = datetime.now()

        return investment
