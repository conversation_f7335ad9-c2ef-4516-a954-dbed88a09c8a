"""Stripe provider implementation."""

from datetime import datetime
from decimal import Decimal
from uuid import uuid4

from ..config import StripeConfig
from ..exceptions import PaymentNotFoundError
from ..factory import register_provider
from ..interfaces import PaymentProvider
from ..models import Payment, PaymentRequest, PaymentStatus
from .base import BaseProvider


@register_provider("stripe")
class StripeProvider(BaseProvider, PaymentProvider):
    """Stripe provider for payment processing."""

    def __init__(self, config: StripeConfig, **kwargs):
        super().__init__(config, **kwargs)
        self.base_url = config.base_url or "https://api.stripe.com/v1"

    def _get_auth_headers(self) -> dict:
        """Get authentication headers."""
        headers = self._get_default_headers()
        headers.update(
            {
                "Authorization": f"Bearer {self.config.api_key}",
            }
        )
        return headers

    async def create_payment(
        self, from_account: str, payment_request: PaymentRequest
    ) -> Payment:
        """Create a payment via Stripe."""
        # In a real implementation, this would create a Stripe payment intent
        # response = await self._make_request(
        #     "POST",
        #     f"{self.base_url}/payment_intents",
        #     headers=self._get_auth_headers(),
        #     json={
        #         "amount": int(payment_request.amount * 100),  # Stripe uses cents
        #         "currency": payment_request.currency.lower(),
        #         "description": payment_request.description,
        #         "metadata": payment_request.metadata
        #     }
        # )

        # Mock Stripe payment intent response
        payment_id = f"pi_{uuid4().hex[:24]}"  # Stripe payment intent ID format

        payment = Payment(
            id=payment_id,
            from_account=from_account,
            to_account=payment_request.to_account,
            amount=payment_request.amount,
            currency=payment_request.currency,
            status=PaymentStatus.PENDING,
            description=payment_request.description,
            reference=payment_request.reference,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            provider_id=payment_id,
            metadata={
                **payment_request.metadata,
                "stripe_payment_intent": payment_id,
                "stripe_status": "requires_payment_method",
            },
        )

        return payment

    async def get_payment(self, payment_id: str) -> Payment:
        """Get payment details from Stripe."""
        # In a real implementation, this would retrieve the payment intent
        # response = await self._make_request(
        #     "GET",
        #     f"{self.base_url}/payment_intents/{payment_id}",
        #     headers=self._get_auth_headers()
        # )

        # Mock response
        if payment_id.startswith("pi_"):
            return Payment(
                id=payment_id,
                from_account="stripe_account_001",
                to_account="recipient_account",
                amount=Decimal("100.00"),
                currency="USD",
                status=PaymentStatus.COMPLETED,
                description="Mock Stripe Payment",
                reference=None,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                provider_id=payment_id,
                metadata={
                    "stripe_payment_intent": payment_id,
                    "stripe_status": "succeeded",
                },
            )
        else:
            raise PaymentNotFoundError(
                f"Payment {payment_id} not found", provider="stripe"
            )

    async def get_payments(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[Payment]:
        """Get payments from Stripe."""
        # In a real implementation, this would list payment intents
        # response = await self._make_request(
        #     "GET",
        #     f"{self.base_url}/payment_intents",
        #     headers=self._get_auth_headers(),
        #     params={
        #         "limit": limit,
        #         "starting_after": offset  # Stripe uses cursor-based pagination
        #     }
        # )

        # Mock payments
        mock_payments = []
        for i in range(min(limit, 5)):  # Return up to 5 mock payments
            payment_id = f"pi_{uuid4().hex[:24]}"
            payment = Payment(
                id=payment_id,
                from_account=account_id,
                to_account=f"recipient_{i}",
                amount=Decimal(f"{100 + i * 50}.00"),
                currency="USD",
                status=PaymentStatus.COMPLETED,
                description=f"Mock Stripe Payment {i + 1}",
                reference=None,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                provider_id=payment_id,
                metadata={
                    "stripe_payment_intent": payment_id,
                    "stripe_status": "succeeded",
                },
            )
            mock_payments.append(payment)

        return mock_payments

    async def cancel_payment(self, payment_id: str) -> Payment:
        """Cancel a payment in Stripe."""
        # In a real implementation, this would cancel the payment intent
        # response = await self._make_request(
        #     "POST",
        #     f"{self.base_url}/payment_intents/{payment_id}/cancel",
        #     headers=self._get_auth_headers()
        # )

        # Mock cancellation
        if payment_id.startswith("pi_"):
            payment = Payment(
                id=payment_id,
                from_account="stripe_account_001",
                to_account="recipient_account",
                amount=Decimal("100.00"),
                currency="USD",
                status=PaymentStatus.CANCELLED,
                description="Mock Stripe Payment (Cancelled)",
                reference=None,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                provider_id=payment_id,
                metadata={
                    "stripe_payment_intent": payment_id,
                    "stripe_status": "canceled",
                },
            )
            return payment
        else:
            raise PaymentNotFoundError(
                f"Payment {payment_id} not found", provider="stripe"
            )
