"""Base provider implementation."""

import asyncio
import logging
from typing import Any

import httpx

from ..config import FinTechSDKConfig
from ..exceptions import NetworkError, ProviderError, TimeoutError


class BaseProvider:
    """Base class for all providers."""

    def __init__(self, config: Any = None, sdk_config: FinTechSDKConfig = None):
        self.config = config
        self.sdk_config = sdk_config or FinTechSDKConfig()
        self.logger = logging.getLogger(
            f"{self.__class__.__module__}.{self.__class__.__name__}"
        )
        self._client: httpx.AsyncClient | None = None

    @property
    def client(self) -> httpx.AsyncClient:
        """Get HTTP client."""
        if self._client is None:
            self._client = httpx.AsyncClient(
                timeout=self.sdk_config.timeout,
                headers=self._get_default_headers(),
            )
        return self._client

    def _get_default_headers(self) -> dict:
        """Get default HTTP headers."""
        return {
            "User-Agent": "fintech-sdk/0.1.0",
            "Content-Type": "application/json",
        }

    async def _make_request(self, method: str, url: str, **kwargs) -> httpx.Response:
        """Make HTTP request with error handling."""
        try:
            if self.sdk_config.log_requests:
                self.logger.info(f"{method.upper()} {url}")

            response = await self.client.request(method, url, **kwargs)

            if self.sdk_config.log_requests:
                self.logger.info(f"Response: {response.status_code}")

            return response

        except httpx.TimeoutException as e:
            raise TimeoutError(
                f"Request timeout: {e}", provider=self.__class__.__name__
            )
        except httpx.NetworkError as e:
            raise NetworkError(f"Network error: {e}", provider=self.__class__.__name__)
        except Exception as e:
            raise ProviderError(
                f"Request failed: {e}", provider=self.__class__.__name__
            )

    async def _handle_response(self, response: httpx.Response) -> dict:
        """Handle HTTP response."""
        if response.status_code >= 400:
            error_msg = f"HTTP {response.status_code}"
            try:
                error_data = response.json()
                if "error" in error_data:
                    error_msg = error_data["error"]
                elif "message" in error_data:
                    error_msg = error_data["message"]
            except Exception:
                error_msg = response.text

            raise ProviderError(
                error_msg,
                provider=self.__class__.__name__,
                status_code=response.status_code,
            )

        try:
            return response.json()
        except Exception:
            return {"data": response.text}

    async def close(self):
        """Close HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()

    def _simulate_delay(self):
        """Simulate network delay for testing."""
        if hasattr(self.config, "delay") and self.config.delay > 0:
            asyncio.sleep(self.config.delay)
