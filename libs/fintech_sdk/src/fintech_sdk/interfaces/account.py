"""Account provider interface."""

from abc import ABC, abstractmethod

from ..models import Account, AccountBalance


class AccountProvider(ABC):
    """Abstract base class for account providers."""

    @abstractmethod
    async def get_account_list(self) -> list[Account]:
        """
        Get list of accounts.

        Returns:
            List of Account objects.

        Raises:
            ProviderError: If the provider request fails.
        """
        pass

    @abstractmethod
    async def get_account_balance(self, account_id: str) -> AccountBalance:
        """
        Get account balance.

        Args:
            account_id: The account identifier.

        Returns:
            AccountBalance object.

        Raises:
            ProviderError: If the provider request fails.
            AccountNotFoundError: If the account is not found.
        """
        pass

    @abstractmethod
    async def get_account(self, account_id: str) -> Account:
        """
        Get account details.

        Args:
            account_id: The account identifier.

        Returns:
            Account object.

        Raises:
            ProviderError: If the provider request fails.
            AccountNotFoundError: If the account is not found.
        """
        pass
