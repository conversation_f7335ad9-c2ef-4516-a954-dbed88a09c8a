"""Transaction provider interface."""

from abc import ABC, abstractmethod
from datetime import datetime

from ..models import Transaction


class TransactionProvider(ABC):
    """Abstract base class for transaction providers."""

    @abstractmethod
    async def get_transactions(
        self,
        account_id: str,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        limit: int | None = None,
        offset: int | None = None,
    ) -> list[Transaction]:
        """
        Get transactions for an account.

        Args:
            account_id: The account identifier.
            start_date: Start date for transaction filtering (optional).
            end_date: End date for transaction filtering (optional).
            limit: Maximum number of transactions to return (optional).
            offset: Number of transactions to skip (optional).

        Returns:
            List of Transaction objects.

        Raises:
            ProviderError: If the provider request fails.
            AccountNotFoundError: If the account is not found.
        """
        pass

    @abstractmethod
    async def get_transaction(self, transaction_id: str) -> Transaction:
        """
        Get a specific transaction.

        Args:
            transaction_id: The transaction identifier.

        Returns:
            Transaction object.

        Raises:
            ProviderError: If the provider request fails.
            TransactionNotFoundError: If the transaction is not found.
        """
        pass
