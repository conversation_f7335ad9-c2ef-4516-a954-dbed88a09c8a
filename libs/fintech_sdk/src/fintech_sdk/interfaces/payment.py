"""Payment provider interface."""

from abc import ABC, abstractmethod

from ..models import Payment, PaymentRequest


class PaymentProvider(ABC):
    """Abstract base class for payment providers."""

    @abstractmethod
    async def create_payment(
        self, from_account: str, payment_request: PaymentRequest
    ) -> Payment:
        """
        Create a payment.

        Args:
            from_account: The source account identifier.
            payment_request: Payment request details.

        Returns:
            Payment object.

        Raises:
            ProviderError: If the provider request fails.
            AccountNotFoundError: If the account is not found.
            InsufficientFundsError: If there are insufficient funds.
        """
        pass

    @abstractmethod
    async def get_payment(self, payment_id: str) -> Payment:
        """
        Get payment details.

        Args:
            payment_id: The payment identifier.

        Returns:
            Payment object.

        Raises:
            ProviderError: If the provider request fails.
            PaymentNotFoundError: If the payment is not found.
        """
        pass

    @abstractmethod
    async def get_payments(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[Payment]:
        """
        Get payments for an account.

        Args:
            account_id: The account identifier.
            limit: Maximum number of payments to return.
            offset: Number of payments to skip.

        Returns:
            List of Payment objects.

        Raises:
            ProviderError: If the provider request fails.
            AccountNotFoundError: If the account is not found.
        """
        pass

    @abstractmethod
    async def cancel_payment(self, payment_id: str) -> Payment:
        """
        Cancel a payment.

        Args:
            payment_id: The payment identifier.

        Returns:
            Updated Payment object.

        Raises:
            ProviderError: If the provider request fails.
            PaymentNotFoundError: If the payment is not found.
            PaymentNotCancellableError: If the payment cannot be cancelled.
        """
        pass
