[project]
name = "fintech-sdk"
version = "0.1.0"
description = "Provider-agnostic fintech integrations SDK"
authors = [
    {name = "FinTech Team", email = "<EMAIL>"},
]
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}

dependencies = [
    # Core dependencies
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    # HTTP client
    "httpx>=0.25.0",
    "requests>=2.31.0",
    # Utilities
    "typing-extensions>=4.8.0",
]

[project.optional-dependencies]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "responses>=0.24.0",
    "coverage>=7.3.0",
]
dev = [
    "mypy>=1.7.0",
    "ruff>=0.1.0",
    "black>=23.0.0",
    "pre-commit>=3.5.0",
]
all = [
    "fintech-sdk[test,dev]",
]

[project.entry-points."fintech_sdk.providers"]
mock = "fintech_sdk.providers.mock:MockProvider"
plaid = "fintech_sdk.providers.plaid:PlaidProvider"
stripe = "fintech_sdk.providers.stripe:StripeProvider"
tink = "fintech_sdk.providers.tink:TinkProvider"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/fintech_sdk"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/README.md",
    "/ARCHITECTURE.md",
]

[tool.pytest.ini_options]
pythonpath = ["src"]
testpaths = ["tests"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--disable-warnings",
]

[tool.mypy]
python_version = "3.10"
strict = false
warn_return_any = false
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = false

[tool.ruff]
target-version = "py310"
line-length = 88

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "B904",  # raise from within except
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101"]  # assert allowed in tests

[tool.black]
target-version = ['py310']
line-length = 88

[dependency-groups]
test = [
    "coverage>=7.9.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.0.0",
    "pytest-mock>=3.14.1",
    "responses>=0.25.7",
]
dev = [
    "black>=25.1.0",
    "mypy>=1.16.1",
]
