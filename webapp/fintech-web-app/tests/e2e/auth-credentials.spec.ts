import { test, expect } from '@playwright/test';

test.describe('Authentication Credentials E2E Tests', () => {
  test.setTimeout(60000); // Increase timeout for auth flows

  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(1000);
  });

  async function navigateToLogin(page) {
    // Navigate through marketing screens to reach login
    const nextButton = page.locator('button:has-text("Next")');
    
    // Navigate through all marketing screens (6 screens total, so 5 clicks to reach the last one)
    for (let i = 0; i < 5; i++) {
      await nextButton.click();
      await page.waitForTimeout(500);
    }

    // Click "Get Started" to go to login screen
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();
    await getStartedButton.click();
    await page.waitForTimeout(2000);

    // Click on Auth0 login
    const auth0Button = page.locator('button:has-text("Continue with Auth0")');
    await expect(auth0Button).toBeVisible();
    await auth0Button.click();
    await page.waitForTimeout(3000);

    // Should be redirected to Auth0 mock server
    await expect(page.url()).toContain('localhost:3002');
    await expect(page.locator('h2:has-text("Sign In")')).toBeVisible({ timeout: 10000 });
  }

  test('should authenticate successfully with correct credentials', async ({ page }) => {
    console.log('🧪 Testing successful authentication with correct credentials');
    
    // Step 1: Navigate to login
    await navigateToLogin(page);

    // Step 2: Fill in correct credentials
    console.log('📝 Filling in correct credentials: <EMAIL> / Test1234');
    await page.locator('input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[name="password"]').first().fill('Test1234');

    // Step 3: Submit login form
    const submitButton = page.locator('button[type="submit"]:has-text("Sign In")');
    await expect(submitButton).toBeVisible();
    
    console.log('🔑 Submitting login form...');
    await submitButton.click();

    // Step 4: Should be redirected back to app and authenticated
    await page.waitForTimeout(5000);
    await expect(page.url()).toContain('localhost:3000');

    console.log('✅ Redirected back to app, verifying authentication...');

    // Step 5: Verify authentication via API
    const response = await page.evaluate(async () => {
      try {
        const res = await fetch('/api/auth/me');
        const data = await res.json();
        return { status: res.status, data };
      } catch (error) {
        return { error: error.message };
      }
    });

    console.log('📊 Auth API response:', response);

    // Step 6: Assert successful authentication
    expect(response.status).toBe(200);
    expect(response.data.authenticated).toBe(true);
    expect(response.data.user.email).toBe('<EMAIL>');

    console.log('🎉 Authentication successful with correct credentials!');
  });

  test('should show error with wrong credentials', async ({ page }) => {
    console.log('🧪 Testing authentication failure with wrong credentials');
    
    // Step 1: Navigate to login
    await navigateToLogin(page);

    // Step 2: Fill in wrong credentials
    console.log('📝 Filling in wrong credentials: <EMAIL> / WrongPassword123');
    await page.locator('input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[name="password"]').first().fill('WrongPassword123');

    // Step 3: Submit login form
    const submitButton = page.locator('button[type="submit"]:has-text("Sign In")');
    await expect(submitButton).toBeVisible();
    
    console.log('🔑 Submitting login form with wrong credentials...');
    await submitButton.click();
    await page.waitForTimeout(2000);

    // Step 4: Should show error message and stay on Auth0 page
    console.log('🔍 Checking for error message...');
    
    // Check for error message on Auth0 mock server
    const errorMessage = page.locator('.error, .alert-danger, [role="alert"], .text-red-500, .text-danger');
    await expect(errorMessage.first()).toBeVisible({ timeout: 5000 });
    
    // Should still be on Auth0 page (not redirected back)
    await expect(page.url()).toContain('localhost:3002');
    
    // Should still see the login form
    await expect(page.locator('h2:has-text("Sign In")')).toBeVisible();
    await expect(page.locator('input[name="email"]').first()).toBeVisible();
    await expect(page.locator('input[name="password"]').first()).toBeVisible();

    console.log('❌ Authentication correctly failed with wrong credentials!');
  });

  test('should show error with non-existent user', async ({ page }) => {
    console.log('🧪 Testing authentication failure with non-existent user');
    
    // Step 1: Navigate to login
    await navigateToLogin(page);

    // Step 2: Fill in non-existent user credentials
    console.log('📝 Filling in non-existent user: <EMAIL> / SomePassword123');
    await page.locator('input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[name="password"]').first().fill('SomePassword123');

    // Step 3: Submit login form
    const submitButton = page.locator('button[type="submit"]:has-text("Sign In")');
    await expect(submitButton).toBeVisible();
    
    console.log('🔑 Submitting login form with non-existent user...');
    await submitButton.click();
    await page.waitForTimeout(2000);

    // Step 4: Should show error message and stay on Auth0 page
    console.log('🔍 Checking for error message...');
    
    // Check for error message on Auth0 mock server
    const errorMessage = page.locator('.error, .alert-danger, [role="alert"], .text-red-500, .text-danger');
    await expect(errorMessage.first()).toBeVisible({ timeout: 5000 });
    
    // Should still be on Auth0 page (not redirected back)
    await expect(page.url()).toContain('localhost:3002');
    
    // Should still see the login form
    await expect(page.locator('h2:has-text("Sign In")')).toBeVisible();

    console.log('❌ Authentication correctly failed with non-existent user!');
  });

  test('should handle empty credentials', async ({ page }) => {
    console.log('🧪 Testing authentication with empty credentials');
    
    // Step 1: Navigate to login
    await navigateToLogin(page);

    // Step 2: Try to submit without filling credentials
    console.log('📝 Attempting to submit empty form...');
    
    const submitButton = page.locator('button[type="submit"]:has-text("Sign In")');
    await expect(submitButton).toBeVisible();
    
    console.log('🔑 Submitting empty login form...');
    await submitButton.click();
    await page.waitForTimeout(1000);

    // Step 3: Should show validation errors or prevent submission
    // The form should either show validation errors or not submit at all
    
    // Should still be on Auth0 page
    await expect(page.url()).toContain('localhost:3002');
    await expect(page.locator('h2:has-text("Sign In")')).toBeVisible();
    
    // Check for validation messages or that fields are still empty
    const emailField = page.locator('input[name="email"]').first();
    const passwordField = page.locator('input[name="password"]').first();
    
    await expect(emailField).toBeVisible();
    await expect(passwordField).toBeVisible();

    console.log('✅ Empty credentials correctly handled!');
  });
});
