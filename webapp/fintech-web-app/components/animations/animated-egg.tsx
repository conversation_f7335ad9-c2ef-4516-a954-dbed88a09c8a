'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Egg } from 'lucide-react';

interface AnimatedEggProps {
  isHatching?: boolean;
  onHatchComplete?: () => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function AnimatedEgg({ 
  isHatching = false, 
  onHatchComplete, 
  size = 'md',
  className = '' 
}: AnimatedEggProps) {
  const [showCracks, setShowCracks] = useState(false);
  const [isHatched, setIsHatched] = useState(false);

  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  useEffect(() => {
    if (isHatching) {
      // Start cracking animation
      const crackTimer = setTimeout(() => {
        setShowCracks(true);
      }, 500);

      // Complete hatching
      const hatchTimer = setTimeout(() => {
        setIsHatched(true);
        onHatchComplete?.();
      }, 2000);

      return () => {
        clearTimeout(crackTimer);
        clearTimeout(hatchTimer);
      };
    }
  }, [isHatching, onHatchComplete]);

  const eggVariants = {
    idle: {
      scale: 1,
      rotate: 0,
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: 'reverse' as const,
        ease: 'easeInOut' as const,
      },
    },
    bounce: {
      scale: [1, 1.1, 1],
      transition: {
        duration: 0.3,
        ease: 'easeInOut' as const,
      },
    },
    cracking: {
      scale: [1, 1.05, 0.95, 1.05, 1],
      rotate: [0, -2, 2, -1, 0],
      transition: {
        duration: 0.5,
        repeat: 3,
        ease: 'easeInOut' as const,
      },
    },
    hatching: {
      scale: [1, 1.2, 0.8, 1.1, 0],
      rotate: [0, 10, -10, 5, 0],
      opacity: [1, 1, 1, 0.5, 0],
      transition: {
        duration: 1.5,
        ease: 'easeInOut' as const,
      },
    },
  };

  const crackVariants = {
    hidden: { opacity: 0, pathLength: 0 },
    visible: {
      opacity: 1,
      pathLength: 1,
      transition: {
        duration: 1,
        ease: 'easeInOut' as const,
      },
    },
  };

  const sparkleVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: {
      scale: [0, 1.2, 1],
      opacity: [0, 1, 0],
      transition: {
        duration: 1,
        repeat: Infinity,
        repeatDelay: 0.5,
      },
    },
  };

  const getAnimationState = () => {
    if (isHatched) return 'hatching';
    if (showCracks) return 'cracking';
    if (isHatching) return 'bounce';
    return 'idle';
  };

  return (
    <div className={`relative ${className}`}>
      <AnimatePresence mode="wait">
        {!isHatched && (
          <motion.div
            key="egg"
            variants={eggVariants}
            animate={getAnimationState()}
            exit="hatching"
            className="relative"
          >
            <div className={`relative ${sizeClasses[size]} text-orange-500`}>
              <Egg className="w-full h-full" />
              
              {/* Crack overlay */}
              <AnimatePresence>
                {showCracks && (
                  <motion.svg
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    className="absolute inset-0 w-full h-full"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <motion.path
                      variants={crackVariants}
                      d="M12 2 L10 8 L14 12 L8 16 L12 22"
                      stroke="#8B5CF6"
                      strokeWidth="1"
                      strokeLinecap="round"
                    />
                    <motion.path
                      variants={crackVariants}
                      d="M8 6 L16 10 L12 14 L18 18"
                      stroke="#8B5CF6"
                      strokeWidth="0.5"
                      strokeLinecap="round"
                      style={{ pathLength: 0.7 }}
                    />
                  </motion.svg>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Sparkles effect */}
      <AnimatePresence>
        {isHatching && (
          <>
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                variants={sparkleVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                className="absolute w-2 h-2 bg-yellow-400 rounded-full"
                style={{
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${i * 0.1}s`,
                }}
              />
            ))}
          </>
        )}
      </AnimatePresence>

      {/* Hatched content */}
      <AnimatePresence>
        {isHatched && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ 
              scale: 1, 
              opacity: 1,
              transition: {
                type: 'spring',
                stiffness: 200,
                damping: 10,
              },
            }}
            className={`${sizeClasses[size]} text-green-500`}
          >
            <div className="w-full h-full bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-bold">✓</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Nest bounce animation component
export function BouncingNest({ children, delay = 0 }: { children: React.ReactNode; delay?: number }) {
  const bounceVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.8
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 200,
        damping: 10,
        delay,
      },
    },
    hover: {
      y: -5,
      scale: 1.05,
      transition: {
        type: 'spring' as const,
        stiffness: 400,
        damping: 10,
      },
    },
  };

  return (
    <motion.div
      variants={bounceVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
    >
      {children}
    </motion.div>
  );
}

// Deposit animation component
export function DepositAnimation({ 
  isActive, 
  onComplete,
  amount 
}: { 
  isActive: boolean; 
  onComplete?: () => void;
  amount?: number;
}) {
  const [showEgg, setShowEgg] = useState(false);

  useEffect(() => {
    if (isActive) {
      setShowEgg(true);
      const timer = setTimeout(() => {
        setShowEgg(false);
        onComplete?.();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isActive, onComplete]);

  return (
    <AnimatePresence>
      {showEgg && (
        <motion.div
          initial={{ y: -100, opacity: 0, scale: 0.5 }}
          animate={{ 
            y: 0, 
            opacity: 1, 
            scale: 1,
            transition: {
              type: 'spring',
              stiffness: 200,
              damping: 15,
            },
          }}
          exit={{ 
            y: 20, 
            opacity: 0, 
            scale: 0.8,
            transition: {
              duration: 0.5,
            },
          }}
          className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50"
        >
          <div className="bg-white rounded-3xl shadow-2xl p-8 text-center">
            <AnimatedEgg 
              isHatching={true} 
              size="lg"
              className="mx-auto mb-4"
            />
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Deposit Successful!
            </h3>
            {amount && (
              <p className="text-lg text-green-600 font-semibold">
                ${amount.toLocaleString()} added to your nest
              </p>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
