'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { useTranslation, formatCurrency } from '../../lib/i18n';
import { PortfolioSummary as PortfolioSummaryType } from '../../lib/types';
import { TrendingUp, TrendingDown, Wallet, PiggyBank, Target, DollarSign } from 'lucide-react';

interface PortfolioSummaryProps {
  summary: PortfolioSummaryType;
}

// Simple sparkline component
function Sparkline({ data, className }: { data: number[]; className?: string }) {
  if (!data || data.length === 0) return null;

  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min;

  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * 100;
    const y = range === 0 ? 50 : ((max - value) / range) * 100;
    return `${x},${y}`;
  }).join(' ');

  return (
    <svg className={`w-16 h-8 ${className}`} viewBox="0 0 100 100" preserveAspectRatio="none">
      <polyline
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        points={points}
      />
    </svg>
  );
}

export function PortfolioSummary({ summary }: PortfolioSummaryProps) {
  const { t, currentLocale } = useTranslation();

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 10,
      },
    },
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  const getGrowthColor = (value: number) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getGrowthIcon = (value: number) => {
    return value >= 0 ? TrendingUp : TrendingDown;
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="mb-8"
    >
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {t('portfolio.summary.title')}
        </h2>
        <p className="text-gray-600">
          {t('portfolio.summary.subtitle')}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Balance */}
        <motion.div variants={itemVariants}>
          <Card className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-blue-700">
                  {t('portfolio.summary.totalBalance')}
                </CardTitle>
                <DollarSign className="h-5 w-5 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900 mb-2">
                {formatCurrency(summary.totalBalance, currentLocale)}
              </div>
              <div className="flex items-center gap-2">
                <Sparkline data={summary.sparklineData} className="text-blue-600" />
                <span className="text-xs text-blue-600">
                  {t('portfolio.summary.trend')}
                </span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Cash Accounts */}
        <motion.div variants={itemVariants}>
          <Card className="relative overflow-hidden bg-gradient-to-br from-green-50 to-emerald-100 border-green-200">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-green-700">
                  {t('portfolio.summary.cashTotal')}
                </CardTitle>
                <Wallet className="h-5 w-5 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-900 mb-2">
                {formatCurrency(summary.cashTotal, currentLocale)}
              </div>
              <div className="flex items-center gap-2">
                {React.createElement(getGrowthIcon(summary.monthlyGrowth), {
                  className: `h-4 w-4 ${getGrowthColor(summary.monthlyGrowth)}`,
                })}
                <span className={`text-sm font-medium ${getGrowthColor(summary.monthlyGrowth)}`}>
                  {formatPercentage(summary.monthlyGrowth)}
                </span>
                <span className="text-xs text-gray-500">
                  {t('portfolio.summary.thisMonth')}
                </span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Investments */}
        <motion.div variants={itemVariants}>
          <Card className="relative overflow-hidden bg-gradient-to-br from-purple-50 to-violet-100 border-purple-200">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-purple-700">
                  {t('portfolio.summary.investmentTotal')}
                </CardTitle>
                <TrendingUp className="h-5 w-5 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-900 mb-2">
                {formatCurrency(summary.investmentTotal, currentLocale)}
              </div>
              <div className="flex items-center gap-2">
                {React.createElement(getGrowthIcon(summary.ytdGrowth), {
                  className: `h-4 w-4 ${getGrowthColor(summary.ytdGrowth)}`,
                })}
                <span className={`text-sm font-medium ${getGrowthColor(summary.ytdGrowth)}`}>
                  {formatPercentage(summary.ytdGrowth)}
                </span>
                <span className="text-xs text-gray-500">
                  {t('portfolio.summary.ytd')}
                </span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Goals/Nests */}
        <motion.div variants={itemVariants}>
          <Card className="relative overflow-hidden bg-gradient-to-br from-orange-50 to-amber-100 border-orange-200">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-orange-700">
                  {t('portfolio.summary.nestsTotal')}
                </CardTitle>
                <Target className="h-5 w-5 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-900 mb-2">
                {formatCurrency(summary.nestsTotal, currentLocale)}
              </div>
              <div className="flex items-center gap-2">
                <PiggyBank className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-700">
                  {t('portfolio.summary.activeNests')}
                </span>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Growth Summary */}
      <motion.div variants={itemVariants} className="mt-6">
        <Card className="bg-gradient-to-r from-gray-50 to-gray-100">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-center sm:text-left">
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {t('portfolio.summary.performanceTitle')}
                </h3>
                <p className="text-sm text-gray-600">
                  {t('portfolio.summary.performanceSubtitle')}
                </p>
              </div>
              <div className="flex gap-6">
                <div className="text-center">
                  <div className="text-sm text-gray-500 mb-1">
                    {t('portfolio.summary.monthlyGrowth')}
                  </div>
                  <Badge
                    variant={summary.monthlyGrowth >= 0 ? 'default' : 'destructive'}
                    className="text-sm font-semibold"
                  >
                    {formatPercentage(summary.monthlyGrowth)}
                  </Badge>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-500 mb-1">
                    {t('portfolio.summary.ytdGrowth')}
                  </div>
                  <Badge
                    variant={summary.ytdGrowth >= 0 ? 'default' : 'destructive'}
                    className="text-sm font-semibold"
                  >
                    {formatPercentage(summary.ytdGrowth)}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
