'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Slider } from '../ui/slider';
import { useTranslation, formatCurrency } from '../../lib/i18n';
import { CompoundingCalculatorInput, CompoundingResult } from '../../lib/types';
import { Calculator, Coffee, ArrowLeft, TrendingUp, DollarSign, Calendar } from 'lucide-react';

interface CompoundingCalculatorProps {
  onBack: () => void;
}

export function CompoundingCalculator({ onBack }: CompoundingCalculatorProps) {
  const { t, currentLocale } = useTranslation();
  const [input, setInput] = useState<CompoundingCalculatorInput>({
    initialAmount: 1000,
    monthlyContribution: 200,
    interestRate: 7,
    years: 10,
  });
  const [results, setResults] = useState<CompoundingResult[]>([]);
  const [animationKey, setAnimationKey] = useState(0);

  const calculateCompounding = useCallback(() => {
    const { initialAmount, monthlyContribution, interestRate, years } = input;
    const monthlyRate = interestRate / 100 / 12;
    const results: CompoundingResult[] = [];

    let balance = initialAmount;
    let totalContributions = initialAmount;

    for (let year = 1; year <= years; year++) {
      for (let month = 1; month <= 12; month++) {
        balance = balance * (1 + monthlyRate) + monthlyContribution;
        totalContributions += monthlyContribution;
      }

      results.push({
        year,
        balance,
        totalContributions,
        interestEarned: balance - totalContributions,
      });
    }

    setResults(results);
    setAnimationKey(prev => prev + 1);
  }, [input]);

  useEffect(() => {
    calculateCompounding();
  }, [input, calculateCompounding]);

  const handleInputChange = (field: keyof CompoundingCalculatorInput, value: number) => {
    setInput(prev => ({ ...prev, [field]: value }));
  };

  const finalResult = results[results.length - 1];
  const totalInterest = finalResult ? finalResult.interestEarned : 0;
  const totalContributions = finalResult ? finalResult.totalContributions : 0;

  // Coffee cup animation data
  const coffeeCups = results.map((result, index) => ({
    year: result.year,
    cups: Math.floor(result.interestEarned / 100), // Each cup represents $100 of interest
    delay: index * 0.1,
  }));

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 10,
      },
    },
  };

  const cupVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: (delay: number) => ({
      scale: 1,
      rotate: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 200,
        damping: 10,
        delay,
      },
    }),
    bounce: {
      scale: [1, 1.2, 1],
      transition: {
        duration: 0.3,
        ease: 'easeInOut' as const,
      },
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center gap-4 mb-6">
            <Button
              variant="ghost"
              onClick={onBack}
              className="gap-2 hover:bg-green-100"
            >
              <ArrowLeft className="h-4 w-4" />
              {t('common.back')}
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {t('calculator.title')}
              </h1>
              <p className="text-gray-600 mt-1">
                {t('calculator.subtitle')}
              </p>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Card className="rounded-3xl shadow-xl bg-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Calculator className="h-6 w-6 text-green-600" />
                  {t('calculator.inputs.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Initial Amount */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <Label htmlFor="initialAmount" className="text-sm font-medium">
                    {t('calculator.inputs.initialAmount')}
                  </Label>
                  <Input
                    id="initialAmount"
                    type="number"
                    value={input.initialAmount}
                    onChange={(e) => handleInputChange('initialAmount', Number(e.target.value))}
                    className="rounded-full"
                  />
                </motion.div>

                {/* Monthly Contribution */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <Label htmlFor="monthlyContribution" className="text-sm font-medium">
                    {t('calculator.inputs.monthlyContribution')}
                  </Label>
                  <Input
                    id="monthlyContribution"
                    type="number"
                    value={input.monthlyContribution}
                    onChange={(e) => handleInputChange('monthlyContribution', Number(e.target.value))}
                    className="rounded-full"
                  />
                </motion.div>

                {/* Interest Rate */}
                <motion.div variants={itemVariants} className="space-y-3">
                  <Label className="text-sm font-medium">
                    {t('calculator.inputs.interestRate')}: {input.interestRate}%
                  </Label>
                  <Slider
                    value={[input.interestRate]}
                    onValueChange={(value) => handleInputChange('interestRate', value[0])}
                    max={15}
                    min={1}
                    step={0.5}
                    className="w-full"
                  />
                </motion.div>

                {/* Years */}
                <motion.div variants={itemVariants} className="space-y-3">
                  <Label className="text-sm font-medium">
                    {t('calculator.inputs.years')}: {input.years} {t('calculator.inputs.yearsUnit')}
                  </Label>
                  <Slider
                    value={[input.years]}
                    onValueChange={(value) => handleInputChange('years', value[0])}
                    max={40}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Results Section */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Card className="rounded-3xl shadow-xl bg-gradient-to-br from-green-500 to-emerald-600 text-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <TrendingUp className="h-6 w-6" />
                  {t('calculator.results.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {finalResult && (
                  <>
                    {/* Final Balance */}
                    <motion.div variants={itemVariants} className="text-center p-4 bg-white/20 rounded-2xl">
                      <div className="text-sm opacity-90 mb-2">
                        {t('calculator.results.finalBalance')}
                      </div>
                      <div className="text-3xl font-bold">
                        {formatCurrency(finalResult.balance, currentLocale)}
                      </div>
                    </motion.div>

                    {/* Breakdown */}
                    <div className="grid grid-cols-2 gap-4">
                      <motion.div variants={itemVariants} className="text-center p-3 bg-white/10 rounded-xl">
                        <DollarSign className="h-5 w-5 mx-auto mb-2 opacity-75" />
                        <div className="text-xs opacity-75 mb-1">
                          {t('calculator.results.totalContributions')}
                        </div>
                        <div className="text-lg font-bold">
                          {formatCurrency(totalContributions, currentLocale)}
                        </div>
                      </motion.div>

                      <motion.div variants={itemVariants} className="text-center p-3 bg-white/10 rounded-xl">
                        <TrendingUp className="h-5 w-5 mx-auto mb-2 opacity-75" />
                        <div className="text-xs opacity-75 mb-1">
                          {t('calculator.results.interestEarned')}
                        </div>
                        <div className="text-lg font-bold">
                          {formatCurrency(totalInterest, currentLocale)}
                        </div>
                      </motion.div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Interactive Chart */}
            {results.length > 0 && (
              <motion.div variants={itemVariants} className="mt-6">
                <Card className="rounded-3xl shadow-xl bg-white">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <TrendingUp className="h-5 w-5 text-green-600" />
                      {t('calculator.chart.title')}
                    </CardTitle>
                    <p className="text-sm text-gray-600">
                      {t('calculator.chart.subtitle')}
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80 w-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={results}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 20,
                          }}
                        >
                          <CartesianGrid
                            strokeDasharray="3 3"
                            stroke="#f0f0f0"
                            opacity={0.6}
                          />
                          <XAxis
                            dataKey="year"
                            stroke="#6b7280"
                            fontSize={12}
                            tickLine={false}
                            axisLine={false}
                          />
                          <YAxis
                            stroke="#6b7280"
                            fontSize={12}
                            tickLine={false}
                            axisLine={false}
                            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                          />
                          <Tooltip
                            contentStyle={{
                              backgroundColor: 'white',
                              border: '1px solid #e5e7eb',
                              borderRadius: '12px',
                              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                              fontSize: '14px'
                            }}
                            formatter={(value: number, name: string) => [
                              formatCurrency(value, currentLocale),
                              name === 'totalContributions'
                                ? t('calculator.chart.totalInvested')
                                : t('calculator.chart.compoundValue')
                            ]}
                            labelFormatter={(year) => `${t('calculator.chart.year')} ${year}`}
                          />
                          <Legend
                            wrapperStyle={{
                              paddingTop: '20px',
                              fontSize: '14px'
                            }}
                            formatter={(value) =>
                              value === 'totalContributions'
                                ? t('calculator.chart.totalInvested')
                                : t('calculator.chart.compoundValue')
                            }
                          />
                          <Line
                            type="monotone"
                            dataKey="totalContributions"
                            stroke="#3b82f6"
                            strokeWidth={3}
                            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                            activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                          />
                          <Line
                            type="monotone"
                            dataKey="balance"
                            stroke="#10b981"
                            strokeWidth={3}
                            dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                            activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Coffee Cup Animation */}
            <Card className="mt-6 rounded-3xl shadow-xl bg-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Coffee className="h-5 w-5 text-amber-600" />
                  {t('calculator.animation.title')}
                </CardTitle>
                <p className="text-sm text-gray-600">
                  {t('calculator.animation.subtitle')}
                </p>
              </CardHeader>
              <CardContent>
                <AnimatePresence mode="wait">
                  <motion.div
                    key={animationKey}
                    className="grid grid-cols-5 gap-2 min-h-[120px]"
                  >
                    {coffeeCups.slice(0, 10).map((yearData) => (
                      <div key={yearData.year} className="text-center">
                        <div className="text-xs text-gray-500 mb-2">
                          {t('calculator.animation.year')} {yearData.year}
                        </div>
                        <div className="flex flex-wrap justify-center gap-1">
                          {Array.from({ length: Math.min(yearData.cups, 5) }).map((_, cupIndex) => (
                            <motion.div
                              key={cupIndex}
                              variants={cupVariants}
                              initial="hidden"
                              animate="visible"
                              whileHover="bounce"
                              custom={yearData.delay + cupIndex * 0.05}
                            >
                              <Coffee className="h-4 w-4 text-amber-600" />
                            </motion.div>
                          ))}
                          {yearData.cups > 5 && (
                            <div className="text-xs text-gray-500">
                              +{yearData.cups - 5}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </motion.div>
                </AnimatePresence>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Yearly Breakdown Table */}
        {results.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-8"
          >
            <Card className="rounded-3xl shadow-xl bg-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  {t('calculator.breakdown.title')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">{t('calculator.breakdown.year')}</th>
                        <th className="text-right py-2">{t('calculator.breakdown.balance')}</th>
                        <th className="text-right py-2">{t('calculator.breakdown.contributions')}</th>
                        <th className="text-right py-2">{t('calculator.breakdown.interest')}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {results.map((result, index) => (
                        <motion.tr
                          key={result.year}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="border-b hover:bg-gray-50"
                        >
                          <td className="py-2 font-medium">{result.year}</td>
                          <td className="text-right py-2 font-bold">
                            {formatCurrency(result.balance, currentLocale)}
                          </td>
                          <td className="text-right py-2">
                            {formatCurrency(result.totalContributions, currentLocale)}
                          </td>
                          <td className="text-right py-2 text-green-600 font-medium">
                            {formatCurrency(result.interestEarned, currentLocale)}
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
}
