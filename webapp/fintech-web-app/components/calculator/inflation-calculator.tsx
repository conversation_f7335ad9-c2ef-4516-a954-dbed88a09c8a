'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { useTranslation, formatCurrency } from '../../lib/i18n';
import { TrendingDown, DollarSign, Calendar, Flag, Info } from 'lucide-react';

interface InflationCalculatorInput {
  initialAmount: number;
  startYear: number;
  endYear: number;
  country: string;
}

interface InflationResult {
  year: number;
  adjustedValue: number;
  cpiIndex: number;
  inflationRate: number;
}

interface InflationCalculatorProps {
  onBack: () => void;
}

// Mock CPI data for different countries
const mockCPIData = {
  usa: {
    name: 'United States',
    currency: 'USD',
    data: Array.from({ length: 26 }, (_, i) => ({
      year: 2000 + i,
      cpi: 172.2 * Math.pow(1.025, i), // ~2.5% average inflation
    }))
  },
  switzerland: {
    name: 'Switzerland',
    currency: 'CHF',
    data: Array.from({ length: 26 }, (_, i) => ({
      year: 2000 + i,
      cpi: 100 * Math.pow(1.008, i), // ~0.8% average inflation
    }))
  },
  germany: {
    name: 'Germany',
    currency: 'EUR',
    data: Array.from({ length: 26 }, (_, i) => ({
      year: 2000 + i,
      cpi: 100 * Math.pow(1.015, i), // ~1.5% average inflation
    }))
  },
  uk: {
    name: 'United Kingdom',
    currency: 'GBP',
    data: Array.from({ length: 26 }, (_, i) => ({
      year: 2000 + i,
      cpi: 100 * Math.pow(1.022, i), // ~2.2% average inflation
    }))
  }
};

export function InflationCalculator({ onBack }: InflationCalculatorProps) {
  const { t, currentLocale } = useTranslation();
  const [input, setInput] = useState<InflationCalculatorInput>({
    initialAmount: 1000,
    startYear: 2000,
    endYear: 2025,
    country: 'switzerland',
  });
  const [results, setResults] = useState<InflationResult[]>([]);

  const calculateInflation = useCallback(() => {
    const { initialAmount, startYear, endYear, country } = input;
    const countryData = mockCPIData[country as keyof typeof mockCPIData];
    
    if (!countryData) return;

    const results: InflationResult[] = [];
    const startCPI = countryData.data.find(d => d.year === startYear)?.cpi || 100;

    for (let year = startYear; year <= endYear; year++) {
      const yearData = countryData.data.find(d => d.year === year);
      if (yearData) {
        const adjustedValue = initialAmount * (startCPI / yearData.cpi);
        const prevYearData = countryData.data.find(d => d.year === year - 1);
        const inflationRate = prevYearData 
          ? ((yearData.cpi - prevYearData.cpi) / prevYearData.cpi) * 100
          : 0;

        results.push({
          year,
          adjustedValue,
          cpiIndex: yearData.cpi,
          inflationRate,
        });
      }
    }

    setResults(results);
  }, [input]);

  useEffect(() => {
    calculateInflation();
  }, [calculateInflation]);

  const handleInputChange = (field: keyof InflationCalculatorInput, value: any) => {
    setInput(prev => ({ ...prev, [field]: value }));
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 10,
      },
    },
  };

  const selectedCountry = mockCPIData[input.country as keyof typeof mockCPIData];
  const finalResult = results[results.length - 1];
  const totalInflation = finalResult 
    ? ((input.initialAmount - finalResult.adjustedValue) / input.initialAmount) * 100
    : 0;

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Input Section */}
      <Card className="rounded-3xl shadow-xl bg-white">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl">
            <TrendingDown className="h-6 w-6 text-red-600" />
            {t('calculator.inflation.title')}
          </CardTitle>
          <p className="text-gray-600">
            {t('calculator.inflation.subtitle')}
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Initial Amount */}
            <motion.div variants={itemVariants} className="space-y-2">
              <Label htmlFor="initialAmount" className="text-sm font-medium">
                {t('calculator.inflation.initialAmount')}
              </Label>
              <Input
                id="initialAmount"
                type="number"
                value={input.initialAmount}
                onChange={(e) => handleInputChange('initialAmount', Number(e.target.value))}
                className="rounded-full"
              />
            </motion.div>

            {/* Country */}
            <motion.div variants={itemVariants} className="space-y-2">
              <Label className="text-sm font-medium">
                {t('calculator.inflation.country')}
              </Label>
              <Select
                value={input.country}
                onValueChange={(value) => handleInputChange('country', value)}
              >
                <SelectTrigger className="rounded-full">
                  <Flag className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="switzerland">🇨🇭 Switzerland (CHF)</SelectItem>
                  <SelectItem value="usa">🇺🇸 United States (USD)</SelectItem>
                  <SelectItem value="germany">🇩🇪 Germany (EUR)</SelectItem>
                  <SelectItem value="uk">🇬🇧 United Kingdom (GBP)</SelectItem>
                </SelectContent>
              </Select>
            </motion.div>

            {/* Start Year */}
            <motion.div variants={itemVariants} className="space-y-2">
              <Label htmlFor="startYear" className="text-sm font-medium">
                {t('calculator.inflation.startYear')}
              </Label>
              <Input
                id="startYear"
                type="number"
                value={input.startYear}
                onChange={(e) => handleInputChange('startYear', Number(e.target.value))}
                className="rounded-full"
                min="2000"
                max="2024"
              />
            </motion.div>

            {/* End Year */}
            <motion.div variants={itemVariants} className="space-y-2">
              <Label htmlFor="endYear" className="text-sm font-medium">
                {t('calculator.inflation.endYear')}
              </Label>
              <Input
                id="endYear"
                type="number"
                value={input.endYear}
                onChange={(e) => handleInputChange('endYear', Number(e.target.value))}
                className="rounded-full"
                min="2001"
                max="2025"
              />
            </motion.div>
          </div>

          {/* Explanation */}
          <motion.div variants={itemVariants} className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="space-y-2">
                <h4 className="font-medium text-blue-900">
                  {t('calculator.inflation.explanation.title')}
                </h4>
                <p className="text-sm text-blue-700">
                  {t('calculator.inflation.explanation.description', {
                    amount: formatCurrency(input.initialAmount, currentLocale),
                    startYear: input.startYear,
                    endYear: input.endYear,
                    country: selectedCountry?.name
                  })}
                </p>
              </div>
            </div>
          </motion.div>
        </CardContent>
      </Card>

      {/* Results Section */}
      {results.length > 0 && (
        <>
          {/* Summary Results */}
          <motion.div variants={itemVariants}>
            <Card className="rounded-3xl shadow-xl bg-gradient-to-br from-red-500 to-pink-600 text-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <DollarSign className="h-6 w-6" />
                  {t('calculator.inflation.results.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-white/20 rounded-2xl">
                    <div className="text-sm opacity-90 mb-2">
                      {t('calculator.inflation.results.originalValue')}
                    </div>
                    <div className="text-2xl font-bold">
                      {formatCurrency(input.initialAmount, currentLocale)}
                    </div>
                    <div className="text-xs opacity-75 mt-1">
                      {input.startYear}
                    </div>
                  </div>

                  <div className="text-center p-4 bg-white/20 rounded-2xl">
                    <div className="text-sm opacity-90 mb-2">
                      {t('calculator.inflation.results.adjustedValue')}
                    </div>
                    <div className="text-2xl font-bold">
                      {finalResult && formatCurrency(finalResult.adjustedValue, currentLocale)}
                    </div>
                    <div className="text-xs opacity-75 mt-1">
                      {input.endYear} {t('calculator.inflation.results.purchasingPower')}
                    </div>
                  </div>

                  <div className="text-center p-4 bg-white/20 rounded-2xl">
                    <div className="text-sm opacity-90 mb-2">
                      {t('calculator.inflation.results.totalInflation')}
                    </div>
                    <div className="text-2xl font-bold">
                      {totalInflation.toFixed(1)}%
                    </div>
                    <div className="text-xs opacity-75 mt-1">
                      {t('calculator.inflation.results.valueDecline')}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Interactive Chart */}
          <motion.div variants={itemVariants}>
            <Card className="rounded-3xl shadow-xl bg-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <TrendingDown className="h-5 w-5 text-red-600" />
                  {t('calculator.inflation.chart.title')}
                </CardTitle>
                <p className="text-sm text-gray-600">
                  {t('calculator.inflation.chart.subtitle')}
                </p>
              </CardHeader>
              <CardContent>
                <div className="h-80 w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={results}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 20,
                      }}
                    >
                      <CartesianGrid 
                        strokeDasharray="3 3" 
                        stroke="#f0f0f0"
                        opacity={0.6}
                      />
                      <XAxis 
                        dataKey="year" 
                        stroke="#6b7280"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis 
                        stroke="#6b7280"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `${formatCurrency(value, currentLocale)}`}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #e5e7eb',
                          borderRadius: '12px',
                          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                          fontSize: '14px'
                        }}
                        formatter={(value: number) => [
                          formatCurrency(value, currentLocale),
                          t('calculator.inflation.chart.purchasingPower')
                        ]}
                        labelFormatter={(year) => `${t('calculator.inflation.chart.year')} ${year}`}
                      />
                      <Legend 
                        wrapperStyle={{
                          paddingTop: '20px',
                          fontSize: '14px'
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="adjustedValue"
                        stroke="#dc2626"
                        strokeWidth={3}
                        dot={{ fill: '#dc2626', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: '#dc2626', strokeWidth: 2 }}
                        name={t('calculator.inflation.chart.purchasingPower')}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </>
      )}
    </motion.div>
  );
}
