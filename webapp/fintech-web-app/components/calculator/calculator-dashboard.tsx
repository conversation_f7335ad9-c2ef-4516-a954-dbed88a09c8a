'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { CompoundingCalculator } from './compounding-calculator';
import { InflationCalculator } from './inflation-calculator';
import { Layout } from '../layout/layout';
import { Calculator, TrendingUp, TrendingDown, ArrowLeft } from 'lucide-react';
import { useTranslation } from '../../lib/i18n';

interface CalculatorDashboardProps {
  onBack?: () => void;
}

type CalculatorType = 'overview' | 'compound' | 'inflation';

export function CalculatorDashboard({ onBack }: CalculatorDashboardProps) {
  const { t } = useTranslation();
  const [activeCalculator, setActiveCalculator] = useState<CalculatorType>('overview');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 10,
      },
    },
  };

  const handleBackToOverview = () => {
    setActiveCalculator('overview');
  };

  if (activeCalculator === 'compound') {
    return (
      <Layout title="Compounding Calculator">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={handleBackToOverview}
              className="gap-2 hover:bg-orange-100"
            >
              <ArrowLeft className="h-4 w-4" />
              {t('common.back')}
            </Button>
          </div>
          <CompoundingCalculator onBack={handleBackToOverview} />
        </div>
      </Layout>
    );
  }

  if (activeCalculator === 'inflation') {
    return (
      <Layout title="Inflation Calculator">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={handleBackToOverview}
              className="gap-2 hover:bg-orange-100"
            >
              <ArrowLeft className="h-4 w-4" />
              {t('common.back')}
            </Button>
          </div>
          <InflationCalculator onBack={handleBackToOverview} />
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Financial Calculators">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {onBack && (
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={onBack}
              className="gap-2 hover:bg-orange-100"
            >
              <ArrowLeft className="h-4 w-4" />
              {t('common.back')}
            </Button>
          </div>
        )}

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="p-3 bg-gradient-to-br from-purple-100 to-violet-100 rounded-full">
                <Calculator className="h-8 w-8 text-purple-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">
                {t('calculator.dashboard.title')}
              </h1>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('calculator.dashboard.subtitle')}
            </p>
          </motion.div>

          {/* Calculator Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Compounding Calculator Card */}
            <motion.div variants={itemVariants}>
              <Card className="h-full rounded-3xl shadow-xl bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 hover:shadow-2xl transition-all duration-300 cursor-pointer group"
                    onClick={() => setActiveCalculator('compound')}>
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full group-hover:scale-110 transition-transform duration-200">
                      <TrendingUp className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl text-green-900">
                      {t('calculator.compound.title')}
                    </CardTitle>
                  </div>
                  <p className="text-green-700">
                    {t('calculator.compound.description')}
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <h4 className="font-semibold text-green-900">
                      {t('calculator.dashboard.features')}:
                    </h4>
                    <ul className="space-y-2 text-sm text-green-700">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        {t('calculator.compound.feature1')}
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        {t('calculator.compound.feature2')}
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        {t('calculator.compound.feature3')}
                      </li>
                    </ul>
                  </div>
                  <Button 
                    className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-full py-3 font-semibold group-hover:scale-105 transition-transform duration-200"
                    onClick={(e) => {
                      e.stopPropagation();
                      setActiveCalculator('compound');
                    }}
                  >
                    {t('calculator.dashboard.startCalculating')}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Inflation Calculator Card */}
            <motion.div variants={itemVariants}>
              <Card className="h-full rounded-3xl shadow-xl bg-gradient-to-br from-red-50 to-pink-50 border-red-200 hover:shadow-2xl transition-all duration-300 cursor-pointer group"
                    onClick={() => setActiveCalculator('inflation')}>
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-3 bg-gradient-to-br from-red-500 to-pink-600 rounded-full group-hover:scale-110 transition-transform duration-200">
                      <TrendingDown className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl text-red-900">
                      {t('calculator.inflation.title')}
                    </CardTitle>
                  </div>
                  <p className="text-red-700">
                    {t('calculator.inflation.description')}
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <h4 className="font-semibold text-red-900">
                      {t('calculator.dashboard.features')}:
                    </h4>
                    <ul className="space-y-2 text-sm text-red-700">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        {t('calculator.inflation.feature1')}
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        {t('calculator.inflation.feature2')}
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        {t('calculator.inflation.feature3')}
                      </li>
                    </ul>
                  </div>
                  <Button 
                    className="w-full bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white rounded-full py-3 font-semibold group-hover:scale-105 transition-transform duration-200"
                    onClick={(e) => {
                      e.stopPropagation();
                      setActiveCalculator('inflation');
                    }}
                  >
                    {t('calculator.dashboard.startCalculating')}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Educational Content */}
          <motion.div variants={itemVariants}>
            <Card className="rounded-3xl shadow-xl bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-xl text-blue-900">
                  {t('calculator.dashboard.education.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-blue-900">
                      {t('calculator.dashboard.education.compoundInterest')}
                    </h4>
                    <p className="text-sm text-blue-700">
                      {t('calculator.dashboard.education.compoundDescription')}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-blue-900">
                      {t('calculator.dashboard.education.inflation')}
                    </h4>
                    <p className="text-sm text-blue-700">
                      {t('calculator.dashboard.education.inflationDescription')}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </Layout>
  );
}
