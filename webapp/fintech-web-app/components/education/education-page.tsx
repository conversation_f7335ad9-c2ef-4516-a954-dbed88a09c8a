'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Layout } from '../layout/layout';
import { 
  GraduationCap, 
  Search, 
  Filter, 
  Clock, 
  User, 
  ArrowRight,
  BookOpen,
  TrendingUp,
  PiggyBank,
  Calculator,
  CreditCard,
  Egg
} from 'lucide-react';
import { useTranslation } from '../../lib/i18n';

interface Article {
  id: string;
  title: string;
  summary: string;
  content: string;
  author: string;
  readTime: number;
  publishedAt: Date;
  tags: string[];
  imageUrl: string;
  featured: boolean;
}

interface EducationPageProps {
  onBack?: () => void;
}

const mockArticles: Article[] = [
  {
    id: '1',
    title: 'The Power of Compound Interest: Your Money\'s Best Friend',
    summary: 'Discover how compound interest can turn small, regular investments into substantial wealth over time.',
    content: 'Compound interest is often called the eighth wonder of the world...',
    author: '<PERSON>',
    readTime: 5,
    publishedAt: new Date('2024-01-15'),
    tags: ['Investing', 'Savings'],
    imageUrl: '/images/compound-interest.jpg',
    featured: true
  },
  {
    id: '2',
    title: 'Building Your Emergency Fund: A Step-by-Step Guide',
    summary: 'Learn how to build a robust emergency fund that protects you from financial surprises.',
    content: 'An emergency fund is your financial safety net...',
    author: 'Michael Chen',
    readTime: 7,
    publishedAt: new Date('2024-01-10'),
    tags: ['Savings', 'Budgeting'],
    imageUrl: '/images/emergency-fund.jpg',
    featured: false
  },
  {
    id: '3',
    title: 'Retirement Planning in Your 20s: Why Starting Early Matters',
    summary: 'Why your 20s are the perfect time to start planning for retirement and how to get started.',
    content: 'Starting retirement planning in your 20s gives you a massive advantage...',
    author: 'Emma Rodriguez',
    readTime: 6,
    publishedAt: new Date('2024-01-05'),
    tags: ['Retirement', 'Investing'],
    imageUrl: '/images/retirement-planning.jpg',
    featured: true
  },
  {
    id: '4',
    title: 'Understanding Credit Cards: Benefits and Pitfalls',
    summary: 'A comprehensive guide to using credit cards responsibly and maximizing their benefits.',
    content: 'Credit cards can be powerful financial tools when used correctly...',
    author: 'David Kim',
    readTime: 8,
    publishedAt: new Date('2023-12-28'),
    tags: ['Budgeting'],
    imageUrl: '/images/credit-cards.jpg',
    featured: false
  },
  {
    id: '5',
    title: 'Investment Basics: Stocks, Bonds, and ETFs Explained',
    summary: 'A beginner-friendly introduction to the most common investment vehicles.',
    content: 'Investing can seem intimidating, but understanding the basics is simpler than you think...',
    author: 'Lisa Thompson',
    readTime: 10,
    publishedAt: new Date('2023-12-20'),
    tags: ['Investing'],
    imageUrl: '/images/investment-basics.jpg',
    featured: false
  }
];

const topicTags = ['All', 'Savings', 'Investing', 'Retirement', 'Budgeting'];

const topicIcons = {
  'Savings': PiggyBank,
  'Investing': TrendingUp,
  'Retirement': Calculator,
  'Budgeting': CreditCard,
};

export function EducationPage({ onBack }: EducationPageProps) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState('All');
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 10,
      },
    },
  };

  // Filter articles
  const filteredArticles = mockArticles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.summary.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTag = selectedTag === 'All' || article.tags.includes(selectedTag);
    return matchesSearch && matchesTag;
  });

  const featuredArticles = filteredArticles.filter(article => article.featured);
  const regularArticles = filteredArticles.filter(article => !article.featured);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  if (selectedArticle) {
    return (
      <Layout title={selectedArticle.title}>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={() => setSelectedArticle(null)}
              className="gap-2 hover:bg-orange-100"
            >
              <ArrowRight className="h-4 w-4 rotate-180" />
              {t('education.backToArticles')}
            </Button>
          </div>

          <motion.article
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-8"
          >
            {/* Article Header */}
            <div className="space-y-4">
              <h1 className="text-3xl font-bold text-gray-900">
                {selectedArticle.title}
              </h1>
              
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>{selectedArticle.author}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>{selectedArticle.readTime} min read</span>
                </div>
                <span>{formatDate(selectedArticle.publishedAt)}</span>
              </div>

              <div className="flex flex-wrap gap-2">
                {selectedArticle.tags.map(tag => {
                  const IconComponent = topicIcons[tag as keyof typeof topicIcons];
                  return (
                    <Badge key={tag} variant="secondary" className="gap-1">
                      {IconComponent && <IconComponent className="h-3 w-3" />}
                      {tag}
                    </Badge>
                  );
                })}
              </div>
            </div>

            {/* Article Content */}
            <Card className="rounded-3xl shadow-xl bg-white">
              <CardContent className="p-8">
                <div className="prose prose-lg max-w-none">
                  <p className="text-xl text-gray-700 leading-relaxed mb-6">
                    {selectedArticle.summary}
                  </p>
                  <div className="text-gray-800 leading-relaxed space-y-4">
                    {selectedArticle.content.split('\n').map((paragraph, index) => (
                      <p key={index}>{paragraph}</p>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Share Buttons */}
            <Card className="rounded-3xl shadow-xl bg-gradient-to-br from-blue-50 to-indigo-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-blue-900 mb-1">
                      {t('education.shareArticle')}
                    </h3>
                    <p className="text-sm text-blue-700">
                      {t('education.shareDescription')}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="rounded-full">
                      {t('education.share.twitter')}
                    </Button>
                    <Button variant="outline" size="sm" className="rounded-full">
                      {t('education.share.linkedin')}
                    </Button>
                    <Button variant="outline" size="sm" className="rounded-full">
                      {t('education.share.copy')}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.article>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Financial Education">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          {/* Hero Banner */}
          <motion.div variants={itemVariants} className="text-center">
            <div className="flex items-center justify-center gap-3 mb-6">
              <motion.div
                whileHover={{ scale: 1.1, rotate: 10 }}
                className="p-4 bg-gradient-to-br from-pink-100 to-rose-100 rounded-full"
              >
                <Egg className="h-10 w-10 text-pink-600" />
              </motion.div>
              <div>
                <h1 className="text-4xl font-bold text-gray-900">
                  {t('education.hero.title')}
                </h1>
                <p className="text-lg text-gray-600 mt-2">
                  {t('education.hero.subtitle')}
                </p>
              </div>
            </div>
          </motion.div>

          {/* Search and Filter */}
          <motion.div variants={itemVariants}>
            <Card className="rounded-3xl shadow-xl bg-white">
              <CardContent className="p-6">
                <div className="flex flex-col sm:flex-row gap-4 items-center">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder={t('education.searchPlaceholder')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 rounded-full border-gray-200 focus:border-pink-300"
                    />
                  </div>
                  <div className="flex gap-2 flex-wrap">
                    {topicTags.map(tag => (
                      <Button
                        key={tag}
                        variant={selectedTag === tag ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setSelectedTag(tag)}
                        className="rounded-full"
                      >
                        {tag === 'All' ? t('education.filters.all') : tag}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Featured Articles */}
          {featuredArticles.length > 0 && (
            <motion.div variants={itemVariants}>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {t('education.featured')}
              </h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {featuredArticles.map((article, index) => (
                  <motion.div
                    key={article.id}
                    variants={itemVariants}
                    whileHover={{ y: -5 }}
                    className="cursor-pointer"
                    onClick={() => setSelectedArticle(article)}
                  >
                    <Card className="h-full rounded-3xl shadow-xl bg-gradient-to-br from-pink-50 to-rose-50 border-pink-200 hover:shadow-2xl transition-all duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between mb-3">
                          <Badge className="bg-pink-100 text-pink-800">
                            {t('education.featured')}
                          </Badge>
                          <div className="flex gap-1">
                            {article.tags.slice(0, 2).map(tag => {
                              const IconComponent = topicIcons[tag as keyof typeof topicIcons];
                              return (
                                <div key={tag} className="p-1 bg-pink-200 rounded-full">
                                  {IconComponent && <IconComponent className="h-3 w-3 text-pink-700" />}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                        <CardTitle className="text-xl text-pink-900 line-clamp-2">
                          {article.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-pink-700 line-clamp-3">
                          {article.summary}
                        </p>
                        <div className="flex items-center justify-between text-sm text-pink-600">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span>{article.author}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{article.readTime} min</span>
                            </div>
                          </div>
                          <ArrowRight className="h-4 w-4" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Regular Articles */}
          {regularArticles.length > 0 && (
            <motion.div variants={itemVariants}>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {t('education.allArticles')}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {regularArticles.map((article) => (
                  <motion.div
                    key={article.id}
                    variants={itemVariants}
                    whileHover={{ y: -5 }}
                    className="cursor-pointer"
                    onClick={() => setSelectedArticle(article)}
                  >
                    <Card className="h-full rounded-3xl shadow-xl bg-white hover:shadow-2xl transition-all duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex gap-1">
                            {article.tags.slice(0, 2).map(tag => {
                              const IconComponent = topicIcons[tag as keyof typeof topicIcons];
                              return (
                                <Badge key={tag} variant="secondary" className="text-xs gap-1">
                                  {IconComponent && <IconComponent className="h-3 w-3" />}
                                  {tag}
                                </Badge>
                              );
                            })}
                          </div>
                          <span className="text-xs text-gray-500">
                            {formatDate(article.publishedAt)}
                          </span>
                        </div>
                        <CardTitle className="text-lg text-gray-900 line-clamp-2">
                          {article.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 line-clamp-3 text-sm">
                          {article.summary}
                        </p>
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span>{article.author}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{article.readTime} min</span>
                            </div>
                          </div>
                          <ArrowRight className="h-4 w-4" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* No Results */}
          {filteredArticles.length === 0 && (
            <motion.div variants={itemVariants} className="text-center py-16">
              <BookOpen className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {t('education.noResults.title')}
              </h3>
              <p className="text-gray-600 mb-6">
                {t('education.noResults.subtitle')}
              </p>
              <Button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedTag('All');
                }}
                className="rounded-full"
              >
                {t('education.noResults.clearFilters')}
              </Button>
            </motion.div>
          )}
        </motion.div>
      </div>
    </Layout>
  );
}
