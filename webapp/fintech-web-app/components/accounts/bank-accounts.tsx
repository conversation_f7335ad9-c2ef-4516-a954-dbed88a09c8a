'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { ConfirmationDialog } from '../ui/confirmation-dialog';
import { useTranslation, formatCurrency } from '../../lib/i18n';
import { BankAccount } from '../../lib/types';
import { 
  Building2, 
  Plus, 
  Trash2, 
  CheckCircle, 
  Clock, 
  ArrowLeft,
  Banknote,
  PiggyBank,
  Link as LinkIcon
} from 'lucide-react';

interface BankAccountsProps {
  accounts: BankAccount[];
  onAddAccount: () => void;
  onRemoveAccount: (accountId: string) => void;
  onBack: () => void;
}

export function BankAccounts({ accounts, onAddAccount, onRemoveAccount, onBack }: BankAccountsProps) {
  const { t, currentLocale } = useTranslation();
  const [accountToRemove, setAccountToRemove] = useState<string | null>(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 10,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 15,
      },
    },
    hover: {
      y: -5,
      scale: 1.02,
      transition: {
        type: 'spring' as const,
        stiffness: 400,
        damping: 10,
      },
    },
  };

  const handleRemoveAccount = (accountId: string) => {
    setAccountToRemove(accountId);
  };

  const confirmRemoveAccount = () => {
    if (accountToRemove) {
      onRemoveAccount(accountToRemove);
      setAccountToRemove(null);
    }
  };

  const getAccountIcon = (accountType: string) => {
    return accountType === 'savings' ? PiggyBank : Banknote;
  };

  const getAccountTypeColor = (accountType: string) => {
    return accountType === 'savings' 
      ? 'bg-green-100 text-green-800 border-green-200'
      : 'bg-blue-100 text-blue-800 border-blue-200';
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(currentLocale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  const totalBalance = accounts.reduce((sum, account) => sum + account.balance, 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="gap-2 hover:bg-blue-100"
              >
                <ArrowLeft className="h-4 w-4" />
                {t('common.back')}
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {t('accounts.bank.title')}
                </h1>
                <p className="text-gray-600 mt-1">
                  {t('accounts.bank.subtitle')}
                </p>
              </div>
            </div>
            <Button
              onClick={onAddAccount}
              className="gap-2 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-full px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-5 w-5" />
              {t('accounts.bank.linkNew')}
            </Button>
          </div>

          {/* Summary Card */}
          <Card className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-3xl shadow-xl">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium opacity-90 mb-2">
                    {t('accounts.bank.totalBalance')}
                  </h3>
                  <div className="text-3xl font-bold">
                    {formatCurrency(totalBalance, currentLocale)}
                  </div>
                  <p className="text-sm opacity-75 mt-2">
                    {t('accounts.bank.accountsCount', { count: accounts.length })}
                  </p>
                </div>
                <div className="p-4 bg-white/20 rounded-full">
                  <Building2 className="h-8 w-8" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Accounts Grid */}
        <AnimatePresence mode="wait">
          {accounts.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-16"
            >
              <Building2 className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {t('accounts.bank.noAccountsTitle')}
              </h3>
              <p className="text-gray-600 mb-6">
                {t('accounts.bank.noAccountsSubtitle')}
              </p>
              <Button
                onClick={onAddAccount}
                className="gap-2 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-full px-6 py-3"
              >
                <LinkIcon className="h-5 w-5" />
                {t('accounts.bank.linkFirst')}
              </Button>
            </motion.div>
          ) : (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {accounts.map((account) => {
                const AccountIcon = getAccountIcon(account.accountType);
                
                return (
                  <motion.div
                    key={account.id}
                    variants={itemVariants}
                  >
                    <motion.div
                      variants={cardVariants}
                      whileHover="hover"
                    >
                      <Card className="relative overflow-hidden bg-gradient-to-br from-white to-gray-50 border-2 hover:border-blue-200 transition-colors duration-200 rounded-3xl shadow-lg hover:shadow-xl">
                        {/* Background Pattern */}
                        <div className="absolute top-0 right-0 w-32 h-32 opacity-5">
                          <div className="w-full h-full rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 transform rotate-12"></div>
                        </div>

                        <CardHeader className="pb-3 relative">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full">
                                <AccountIcon className="h-6 w-6 text-blue-600" />
                              </div>
                              <div>
                                <CardTitle className="text-lg font-bold text-gray-900">
                                  {account.name}
                                </CardTitle>
                                <p className="text-sm text-gray-600">
                                  {account.bankName}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {account.isLinked ? (
                                <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  {t('accounts.bank.linked')}
                                </Badge>
                              ) : (
                                <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {t('accounts.bank.pending')}
                                </Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveAccount(account.id)}
                                className="h-8 w-8 p-0 hover:bg-red-100"
                                aria-label={`${t('accounts.bank.remove')} ${account.name}`}
                              >
                                <Trash2 className="h-4 w-4 text-red-600" />
                              </Button>
                            </div>
                          </div>
                          <Badge className={`w-fit ${getAccountTypeColor(account.accountType)}`} variant="secondary">
                            {t(`accounts.bank.types.${account.accountType}`)}
                          </Badge>
                        </CardHeader>

                        <CardContent className="space-y-4">
                          {/* Balance */}
                          <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl">
                            <div className="text-sm text-blue-600 font-medium mb-1">
                              {t('accounts.bank.balance')}
                            </div>
                            <div className="text-2xl font-bold text-blue-900">
                              {formatCurrency(account.balance, currentLocale)}
                            </div>
                          </div>

                          {/* Account Details */}
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">{t('accounts.bank.accountNumber')}</span>
                              <span className="font-medium">****{account.lastFour}</span>
                            </div>
                            {account.linkedDate && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">{t('accounts.bank.linkedDate')}</span>
                                <span className="font-medium">{formatDate(account.linkedDate)}</span>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </motion.div>
                );
              })}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Remove Confirmation Dialog */}
        <ConfirmationDialog
          open={!!accountToRemove}
          onOpenChange={(open) => !open && setAccountToRemove(null)}
          title={t('accounts.bank.removeConfirmation.title')}
          description={t('accounts.bank.removeConfirmation.description')}
          confirmText={t('common.remove')}
          cancelText={t('common.cancel')}
          onConfirm={confirmRemoveAccount}
          variant="destructive"
        />
      </div>
    </div>
  );
}
