'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { useTranslation, formatCurrency } from '../../lib/i18n';
import { RetirementAccount } from '../../lib/types';
import { retirementAccountTypes } from '../../lib/constants';
import { 
  PiggyBank, 
  Plus, 
  ArrowLeft,
  TrendingUp,
  Calendar,
  Target,
  Flag,
  DollarSign
} from 'lucide-react';

interface RetirementAccountsProps {
  accounts: RetirementAccount[];
  onAddAccount: (accountData: any) => void;
  onBack: () => void;
}

export function RetirementAccounts({ accounts, onAddAccount, onBack }: RetirementAccountsProps) {
  const { t, currentLocale } = useTranslation();
  const [selectedCountry, setSelectedCountry] = useState<string>('switzerland');
  const [showAddForm, setShowAddForm] = useState(false);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 10,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 15,
      },
    },
    hover: {
      y: -5,
      scale: 1.02,
      transition: {
        type: 'spring' as const,
        stiffness: 400,
        damping: 10,
      },
    },
  };

  const totalBalance = accounts.reduce((sum, account) => sum + account.balance, 0);
  const totalContributions = accounts.reduce((sum, account) => sum + account.currentYearContribution, 0);
  const totalLimit = accounts.reduce((sum, account) => sum + account.contributionLimit, 0);

  const getContributionProgress = (account: RetirementAccount) => {
    return (account.currentYearContribution / account.contributionLimit) * 100;
  };

  const getAccountTypeColor = (type: string) => {
    const colors = {
      swiss_3a: 'bg-red-100 text-red-800 border-red-200',
      german_riester: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      us_401k: 'bg-blue-100 text-blue-800 border-blue-200',
      uk_pension: 'bg-purple-100 text-purple-800 border-purple-200',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const handleAddAccount = () => {
    const accountTypes = retirementAccountTypes[selectedCountry as keyof typeof retirementAccountTypes];
    if (accountTypes && accountTypes.length > 0) {
      const selectedType = accountTypes[0];
      onAddAccount({
        type: selectedType.value,
        country: selectedCountry,
        contributionLimit: selectedType.limit,
      });
      setShowAddForm(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="gap-2 hover:bg-purple-100"
              >
                <ArrowLeft className="h-4 w-4" />
                {t('common.back')}
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {t('accounts.retirement.title')}
                </h1>
                <p className="text-gray-600 mt-1">
                  {t('accounts.retirement.subtitle')}
                </p>
              </div>
            </div>
            <Button
              onClick={() => setShowAddForm(true)}
              className="gap-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white rounded-full px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-5 w-5" />
              {t('accounts.retirement.addNew')}
            </Button>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card className="bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-3xl shadow-xl">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium opacity-90 mb-2">
                      {t('accounts.retirement.totalBalance')}
                    </h3>
                    <div className="text-2xl font-bold">
                      {formatCurrency(totalBalance, currentLocale)}
                    </div>
                  </div>
                  <PiggyBank className="h-8 w-8 opacity-75" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-3xl shadow-xl">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium opacity-90 mb-2">
                      {t('accounts.retirement.thisYearContributions')}
                    </h3>
                    <div className="text-2xl font-bold">
                      {formatCurrency(totalContributions, currentLocale)}
                    </div>
                  </div>
                  <TrendingUp className="h-8 w-8 opacity-75" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-orange-500 to-amber-600 text-white rounded-3xl shadow-xl">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium opacity-90 mb-2">
                      {t('accounts.retirement.remainingLimit')}
                    </h3>
                    <div className="text-2xl font-bold">
                      {formatCurrency(totalLimit - totalContributions, currentLocale)}
                    </div>
                  </div>
                  <Target className="h-8 w-8 opacity-75" />
                </div>
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* Add Account Form */}
        <AnimatePresence>
          {showAddForm && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-8"
            >
              <Card className="rounded-3xl shadow-xl bg-white">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Plus className="h-5 w-5 text-purple-600" />
                    {t('accounts.retirement.addNew')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {t('accounts.retirement.selectCountry')}
                    </label>
                    <Select value={selectedCountry} onValueChange={setSelectedCountry}>
                      <SelectTrigger className="rounded-full">
                        <Flag className="h-4 w-4 mr-2" />
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="switzerland">🇨🇭 Switzerland</SelectItem>
                        <SelectItem value="germany">🇩🇪 Germany</SelectItem>
                        <SelectItem value="usa">🇺🇸 United States</SelectItem>
                        <SelectItem value="uk">🇬🇧 United Kingdom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedCountry && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        {t('accounts.retirement.availableTypes')}
                      </label>
                      <div className="space-y-2">
                        {retirementAccountTypes[selectedCountry as keyof typeof retirementAccountTypes]?.map((type) => (
                          <div key={type.value} className="p-3 border rounded-xl hover:bg-gray-50">
                            <div className="flex justify-between items-center">
                              <div>
                                <div className="font-medium">{type.label}</div>
                                <div className="text-sm text-gray-600">
                                  {t('accounts.retirement.annualLimit')}: {formatCurrency(type.limit, currentLocale)}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex gap-2 pt-4">
                    <Button
                      onClick={handleAddAccount}
                      className="flex-1 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white rounded-full"
                    >
                      {t('accounts.retirement.create')}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setShowAddForm(false)}
                      className="flex-1 rounded-full"
                    >
                      {t('common.cancel')}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Accounts Grid */}
        <AnimatePresence mode="wait">
          {accounts.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-16"
            >
              <PiggyBank className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {t('accounts.retirement.noAccountsTitle')}
              </h3>
              <p className="text-gray-600 mb-6">
                {t('accounts.retirement.noAccountsSubtitle')}
              </p>
              <Button
                onClick={() => setShowAddForm(true)}
                className="gap-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white rounded-full px-6 py-3"
              >
                <Plus className="h-5 w-5" />
                {t('accounts.retirement.createFirst')}
              </Button>
            </motion.div>
          ) : (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {accounts.map((account) => {
                const progress = getContributionProgress(account);
                const remaining = account.contributionLimit - account.currentYearContribution;
                
                return (
                  <motion.div
                    key={account.id}
                    variants={itemVariants}
                  >
                    <motion.div
                      variants={cardVariants}
                      whileHover="hover"
                    >
                      <Card className="relative overflow-hidden bg-gradient-to-br from-white to-gray-50 border-2 hover:border-purple-200 transition-colors duration-200 rounded-3xl shadow-lg hover:shadow-xl">
                        {/* Background Pattern */}
                        <div className="absolute top-0 right-0 w-32 h-32 opacity-5">
                          <div className="w-full h-full rounded-full bg-gradient-to-br from-purple-400 to-indigo-500 transform rotate-12"></div>
                        </div>

                        <CardHeader className="pb-3 relative">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-full">
                                <PiggyBank className="h-6 w-6 text-purple-600" />
                              </div>
                              <div>
                                <CardTitle className="text-lg font-bold text-gray-900">
                                  {account.name}
                                </CardTitle>
                                <p className="text-sm text-gray-600">
                                  {account.country}
                                </p>
                              </div>
                            </div>
                          </div>
                          <Badge className={`w-fit ${getAccountTypeColor(account.type)}`} variant="secondary">
                            {retirementAccountTypes[account.country.toLowerCase() as keyof typeof retirementAccountTypes]?.find(
                              type => type.value === account.type
                            )?.label || account.type}
                          </Badge>
                        </CardHeader>

                        <CardContent className="space-y-4">
                          {/* Balance */}
                          <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl">
                            <div className="text-sm text-purple-600 font-medium mb-1">
                              {t('accounts.retirement.currentBalance')}
                            </div>
                            <div className="text-2xl font-bold text-purple-900">
                              {formatCurrency(account.balance, currentLocale)}
                            </div>
                          </div>

                          {/* Contribution Progress */}
                          <div className="space-y-3">
                            <div className="flex justify-between items-center text-sm">
                              <span className="font-medium text-gray-700">
                                {t('accounts.retirement.contributionProgress')}
                              </span>
                              <span className="font-bold text-gray-900">
                                {progress.toFixed(1)}%
                              </span>
                            </div>
                            <Progress value={progress} className="h-3 bg-gray-200" />
                            <div className="flex justify-between text-xs text-gray-600">
                              <span>
                                {formatCurrency(account.currentYearContribution, currentLocale)}
                              </span>
                              <span>
                                {formatCurrency(account.contributionLimit, currentLocale)}
                              </span>
                            </div>
                          </div>

                          {/* Remaining Contribution */}
                          <div className="text-center p-3 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
                            <div className="flex items-center justify-center gap-1 text-green-600 mb-1">
                              <DollarSign className="h-4 w-4" />
                              <span className="text-xs font-medium">
                                {t('accounts.retirement.remainingContribution')}
                              </span>
                            </div>
                            <div className="text-lg font-bold text-green-800">
                              {formatCurrency(remaining, currentLocale)}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </motion.div>
                );
              })}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
