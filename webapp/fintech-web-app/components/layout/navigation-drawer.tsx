'use client';

import { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { Button } from '../ui/button';
import { 
  Home, 
  Target, 
  Building2, 
  Calculator, 
  GraduationCap, 
  Settings, 
  LogOut,
  Egg,
  X
} from 'lucide-react';

interface NavigationDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  description: string;
  gradient: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: Home,
    href: '/fintech',
    description: 'Portfolio overview & summary',
    gradient: 'from-blue-500 to-indigo-600'
  },
  {
    id: 'goals',
    label: 'Goals',
    icon: Target,
    href: '/fintech/goals',
    description: 'Manage your financial nests',
    gradient: 'from-orange-500 to-amber-600'
  },
  {
    id: 'accounts',
    label: 'Accounts',
    icon: Building2,
    href: '/fintech/accounts',
    description: 'Bank & retirement accounts',
    gradient: 'from-green-500 to-emerald-600'
  },
  {
    id: 'calculator',
    label: 'Calculator',
    icon: Calculator,
    href: '/fintech/calculator',
    description: 'Compound & inflation tools',
    gradient: 'from-purple-500 to-violet-600'
  },
  {
    id: 'education',
    label: 'Education',
    icon: GraduationCap,
    href: '/fintech/education',
    description: 'Learn to grow your nest',
    gradient: 'from-pink-500 to-rose-600'
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    href: '/fintech/settings',
    description: 'Account & preferences',
    gradient: 'from-gray-500 to-slate-600'
  }
];

export function NavigationDrawer({ isOpen, onClose }: NavigationDrawerProps) {
  const router = useRouter();
  const drawerRef = useRef<HTMLDivElement>(null);
  const firstFocusableRef = useRef<HTMLButtonElement>(null);

  // Focus management
  useEffect(() => {
    if (isOpen) {
      // Focus the first focusable element when drawer opens
      setTimeout(() => {
        firstFocusableRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Keyboard event handling
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      if (event.key === 'Escape') {
        onClose();
      }

      // Focus trap
      if (event.key === 'Tab') {
        const focusableElements = drawerRef.current?.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements && focusableElements.length > 0) {
          const firstElement = focusableElements[0] as HTMLElement;
          const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

          if (event.shiftKey && document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
          } else if (!event.shiftKey && document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  const handleNavigation = (href: string) => {
    router.push(href);
    onClose();
  };

  const handleLogout = () => {
    // TODO: Implement logout logic
    console.log('Logout clicked');
    onClose();
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  const drawerVariants = {
    hidden: { x: '100%' },
    visible: {
      x: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      x: '100%',
      transition: {
        type: 'spring' as const,
        stiffness: 300,
        damping: 30
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: (index: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: index * 0.1,
        type: 'spring' as const,
        stiffness: 200,
        damping: 20
      }
    })
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Overlay */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
            aria-hidden="true"
          />

          {/* Drawer */}
          <motion.div
            ref={drawerRef}
            variants={drawerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="fixed top-0 right-0 z-50 h-full w-full max-w-sm bg-white shadow-2xl"
            role="dialog"
            aria-modal="true"
            aria-labelledby="navigation-drawer-title"
            id="navigation-drawer"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-orange-100 to-amber-100 rounded-full">
                  <Egg className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <h2 id="navigation-drawer-title" className="text-lg font-bold text-gray-900">
                    Navigation
                  </h2>
                  <p className="text-sm text-gray-500">Choose your destination</p>
                </div>
              </div>
              <Button
                ref={firstFocusableRef}
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-full"
                aria-label="Close navigation menu"
              >
                <X className="h-5 w-5 text-gray-600" />
              </Button>
            </div>

            {/* Navigation Items */}
            <div className="flex-1 overflow-y-auto p-6">
              <nav className="space-y-3" role="navigation" aria-label="Main navigation">
                {navigationItems.map((item, index) => {
                  const IconComponent = item.icon;
                  return (
                    <motion.div
                      key={item.id}
                      variants={itemVariants}
                      initial="hidden"
                      animate="visible"
                      custom={index}
                    >
                      <Button
                        variant="ghost"
                        onClick={() => handleNavigation(item.href)}
                        className="w-full p-4 h-auto justify-start text-left hover:bg-gray-50 rounded-2xl group transition-all duration-200"
                      >
                        <div className="flex items-center gap-4 w-full">
                          <div className={`p-3 bg-gradient-to-br ${item.gradient} rounded-xl group-hover:scale-105 transition-transform duration-200`}>
                            <IconComponent className="h-5 w-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="font-semibold text-gray-900 group-hover:text-gray-700">
                              {item.label}
                            </div>
                            <div className="text-sm text-gray-500 group-hover:text-gray-600">
                              {item.description}
                            </div>
                          </div>
                        </div>
                      </Button>
                    </motion.div>
                  );
                })}
              </nav>
            </div>

            {/* Footer */}
            <div className="p-6 border-t border-gray-100">
              <Button
                variant="ghost"
                onClick={handleLogout}
                className="w-full p-4 h-auto justify-start text-left hover:bg-red-50 rounded-2xl group transition-all duration-200"
              >
                <div className="flex items-center gap-4 w-full">
                  <div className="p-3 bg-gradient-to-br from-red-500 to-red-600 rounded-xl group-hover:scale-105 transition-transform duration-200">
                    <LogOut className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-gray-900 group-hover:text-red-700">
                      Sign Out
                    </div>
                    <div className="text-sm text-gray-500 group-hover:text-red-600">
                      Return to login screen
                    </div>
                  </div>
                </div>
              </Button>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
