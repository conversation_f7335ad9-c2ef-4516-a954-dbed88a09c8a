'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '../ui/button';
import { Menu, X, Egg } from 'lucide-react';
import { NavigationDrawer } from './navigation-drawer';

interface HeaderProps {
  title?: string;
  showLogo?: boolean;
}

export function Header({ title = "Nest Finance", showLogo = true }: HeaderProps) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };

  return (
    <>
      <motion.header
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="sticky top-0 z-40 w-full bg-white/95 backdrop-blur-sm border-b border-orange-100 shadow-sm"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Title */}
            <div className="flex items-center gap-3">
              {showLogo && (
                <motion.div
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-2 bg-gradient-to-br from-orange-100 to-amber-100 rounded-full"
                >
                  <Egg className="h-6 w-6 text-orange-600" />
                </motion.div>
              )}
              <div>
                <h1 className="text-xl font-bold text-gray-900">{title}</h1>
                <p className="text-xs text-gray-500 hidden sm:block">
                  Grow your financial nest
                </p>
              </div>
            </div>

            {/* Hamburger Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleDrawer}
              className="relative p-2 hover:bg-orange-50 rounded-full transition-colors"
              aria-label="Open navigation menu"
              aria-expanded={isDrawerOpen}
              aria-controls="navigation-drawer"
            >
              <motion.div
                animate={{ rotate: isDrawerOpen ? 90 : 0 }}
                transition={{ duration: 0.2 }}
              >
                {isDrawerOpen ? (
                  <X className="h-6 w-6 text-gray-700" />
                ) : (
                  <Menu className="h-6 w-6 text-gray-700" />
                )}
              </motion.div>
            </Button>
          </div>
        </div>
      </motion.header>

      {/* Navigation Drawer */}
      <NavigationDrawer 
        isOpen={isDrawerOpen} 
        onClose={closeDrawer}
      />
    </>
  );
}
