'use client';

import { motion } from 'framer-motion';
import { Egg, Heart } from 'lucide-react';

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <motion.footer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.5 }}
      className="bg-white/80 backdrop-blur-sm border-t border-orange-100 py-6 mt-auto"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          {/* Logo and Brand */}
          <div className="flex items-center gap-3">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 10 }}
              className="p-2 bg-gradient-to-br from-orange-100 to-amber-100 rounded-full"
            >
              <Egg className="h-5 w-5 text-orange-600" />
            </motion.div>
            <div className="text-center sm:text-left">
              <div className="text-sm font-semibold text-gray-900">Nest Finance</div>
              <div className="text-xs text-gray-500">Grow your financial future</div>
            </div>
          </div>

          {/* Copyright and Love */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>© {currentYear} Nest Finance</span>
            <span>•</span>
            <div className="flex items-center gap-1">
              <span>Made with</span>
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity, repeatDelay: 2 }}
              >
                <Heart className="h-4 w-4 text-red-500 fill-current" />
              </motion.div>
              <span>for your financial growth</span>
            </div>
          </div>
        </div>
      </div>
    </motion.footer>
  );
}
