'use client';

import { motion } from 'framer-motion';
import { Header } from './header';
import { Footer } from './footer';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  showHeader?: boolean;
  showFooter?: boolean;
  className?: string;
}

export function Layout({ 
  children, 
  title,
  showHeader = true, 
  showFooter = true,
  className = ''
}: LayoutProps) {
  const pageVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: 'easeOut' as const
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.3,
        ease: 'easeIn' as const
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex flex-col">
      {showHeader && <Header title={title} />}
      
      <motion.main
        variants={pageVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className={`flex-1 ${className}`}
      >
        {children}
      </motion.main>
      
      {showFooter && <Footer />}
    </div>
  );
}
