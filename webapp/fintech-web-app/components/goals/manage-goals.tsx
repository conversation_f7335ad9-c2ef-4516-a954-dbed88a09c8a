'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { ConfirmationDialog } from '../ui/confirmation-dialog';
import { useTranslation, formatCurrency } from '../../lib/i18n';
import { Goal } from '../../lib/types';
import { NestCard } from './nest-card';
import { EnhancedGoalForm } from './enhanced-goal-form';
import { Plus, Grid, List, Search, Filter, ArrowLeft, Target } from 'lucide-react';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

interface ManageGoalsProps {
  goals: Goal[];
  onAddGoal: (goalData: any) => void;
  onEditGoal: (goal: Goal) => void;
  onDeleteGoal: (goalId: string) => void;
  onBack: () => void;
}

type ViewMode = 'grid' | 'list';
type FilterType = 'all' | 'active' | 'completed' | 'fixed-income' | 'standard' | 'growth';

export function ManageGoals({ 
  goals, 
  onAddGoal, 
  onEditGoal, 
  onDeleteGoal, 
  onBack 
}: ManageGoalsProps) {
  const { t, currentLocale } = useTranslation();
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<FilterType>('all');
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(null);
  const [goalToDelete, setGoalToDelete] = useState<string | null>(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 10,
      },
    },
  };

  // Filter and search goals
  const filteredGoals = goals.filter(goal => {
    const matchesSearch = goal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (goal.description && goal.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesFilter = (() => {
      switch (filter) {
        case 'active':
          return goal.isActive;
        case 'completed':
          return goal.saved >= goal.target;
        case 'fixed-income':
          return goal.strategy === 'Fixed Income';
        case 'standard':
          return goal.strategy === 'Standard';
        case 'growth':
          return goal.strategy === 'Growth';
        default:
          return true;
      }
    })();

    return matchesSearch && matchesFilter;
  });

  const handleAddGoal = (goalData: any) => {
    onAddGoal(goalData);
    setShowAddForm(false);
  };

  const handleEditGoal = (goal: Goal) => {
    setSelectedGoal(goal);
  };

  const handleDeleteGoal = (goalId: string) => {
    setGoalToDelete(goalId);
  };

  const confirmDeleteGoal = () => {
    if (goalToDelete) {
      onDeleteGoal(goalToDelete);
      setGoalToDelete(null);
    }
  };

  const handleGoalClick = (goal: Goal) => {
    setSelectedGoal(goal);
  };

  const getFilterCount = (filterType: FilterType) => {
    switch (filterType) {
      case 'active':
        return goals.filter(g => g.isActive).length;
      case 'completed':
        return goals.filter(g => g.saved >= g.target).length;
      case 'fixed-income':
        return goals.filter(g => g.strategy === 'Fixed Income').length;
      case 'standard':
        return goals.filter(g => g.strategy === 'Standard').length;
      case 'growth':
        return goals.filter(g => g.strategy === 'Growth').length;
      default:
        return goals.length;
    }
  };

  if (showAddForm) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={() => setShowAddForm(false)}
              className="gap-2 hover:bg-orange-100"
            >
              <ArrowLeft className="h-4 w-4" />
              {t('common.back')}
            </Button>
          </div>
          <EnhancedGoalForm
            onSubmit={handleAddGoal}
            onCancel={() => setShowAddForm(false)}
          />
        </div>
      </div>
    );
  }

  if (selectedGoal) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={() => setSelectedGoal(null)}
              className="gap-2 hover:bg-orange-100"
            >
              <ArrowLeft className="h-4 w-4" />
              {t('common.back')}
            </Button>
          </div>
          <EnhancedGoalForm
            goal={selectedGoal}
            onSubmit={(goalData) => {
              onEditGoal({ ...selectedGoal, ...goalData });
              setSelectedGoal(null);
            }}
            onCancel={() => setSelectedGoal(null)}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="gap-2 hover:bg-orange-100"
              >
                <ArrowLeft className="h-4 w-4" />
                {t('common.back')}
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {t('goals.manage.title')}
                </h1>
                <p className="text-gray-600 mt-1">
                  {t('goals.manage.subtitle')}
                </p>
              </div>
            </div>
            <Button
              onClick={() => setShowAddForm(true)}
              className="gap-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white rounded-full px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-5 w-5" />
              {t('goals.addNew')}
            </Button>
          </div>

          {/* Controls */}
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between bg-white rounded-2xl p-4 shadow-sm">
            <div className="flex gap-4 items-center flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder={t('goals.manage.searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 rounded-full border-gray-200 focus:border-orange-300"
                />
              </div>
              <Select value={filter} onValueChange={(value: FilterType) => setFilter(value)}>
                <SelectTrigger className="w-48 rounded-full border-gray-200">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {t('goals.manage.filters.all')} ({getFilterCount('all')})
                  </SelectItem>
                  <SelectItem value="active">
                    {t('goals.manage.filters.active')} ({getFilterCount('active')})
                  </SelectItem>
                  <SelectItem value="completed">
                    {t('goals.manage.filters.completed')} ({getFilterCount('completed')})
                  </SelectItem>
                  <SelectItem value="fixed-income">
                    {t('goals.strategies.fixedincome')} ({getFilterCount('fixed-income')})
                  </SelectItem>
                  <SelectItem value="standard">
                    {t('goals.strategies.standard')} ({getFilterCount('standard')})
                  </SelectItem>
                  <SelectItem value="growth">
                    {t('goals.strategies.growth')} ({getFilterCount('growth')})
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-full"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-full"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Goals Grid/List */}
        <AnimatePresence mode="wait">
          {filteredGoals.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-16"
            >
              <Target className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {searchTerm || filter !== 'all' 
                  ? t('goals.manage.noResultsTitle')
                  : t('goals.manage.noGoalsTitle')
                }
              </h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || filter !== 'all'
                  ? t('goals.manage.noResultsSubtitle')
                  : t('goals.manage.noGoalsSubtitle')
                }
              </p>
              {(!searchTerm && filter === 'all') && (
                <Button
                  onClick={() => setShowAddForm(true)}
                  className="gap-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white rounded-full px-6 py-3"
                >
                  <Plus className="h-5 w-5" />
                  {t('goals.addNew')}
                </Button>
              )}
            </motion.div>
          ) : (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                  : 'space-y-4'
              }
            >
              {filteredGoals.map((goal) => (
                <motion.div key={goal.id} variants={itemVariants}>
                  <NestCard
                    goal={goal}
                    onEdit={handleEditGoal}
                    onDelete={handleDeleteGoal}
                    onClick={handleGoalClick}
                  />
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Delete Confirmation Dialog */}
        <ConfirmationDialog
          open={!!goalToDelete}
          onOpenChange={(open) => !open && setGoalToDelete(null)}
          title={t('goals.deleteConfirmation.title')}
          description={t('goals.deleteConfirmation.description')}
          confirmText={t('common.delete')}
          cancelText={t('common.cancel')}
          onConfirm={confirmDeleteGoal}
          variant="destructive"
        />
      </div>
    </div>
  );
}
