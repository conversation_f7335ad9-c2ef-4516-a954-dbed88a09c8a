'use client';

import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Button } from '../ui/button';
import { useTranslation, formatCurrency } from '../../lib/i18n';
import { Goal } from '../../lib/types';
import { portfolioAllocations } from '../../lib/constants';
import { Target, Calendar, TrendingUp, Edit, Trash2, Egg } from 'lucide-react';

interface NestCardProps {
  goal: Goal;
  onEdit: (goal: Goal) => void;
  onDelete: (goalId: string) => void;
  onClick: (goal: Goal) => void;
}

export function NestCard({ goal, onEdit, onDelete, onClick }: NestCardProps) {
  const { t, currentLocale } = useTranslation();

  const progressPercentage = Math.min((goal.saved / goal.target) * 100, 100);
  const remaining = Math.max(goal.target - goal.saved, 0);
  const allocation = goal.allocation || portfolioAllocations[goal.strategy];

  const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15,
        duration: 0.6,
      },
    },
    hover: {
      y: -5,
      scale: 1.02,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 10,
      },
    },
    tap: {
      scale: 0.98,
    },
  };

  const eggVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        type: 'spring',
        stiffness: 200,
        damping: 10,
        delay: 0.2,
      },
    },
    bounce: {
      scale: [1, 1.1, 1],
      transition: {
        duration: 0.3,
        ease: 'easeInOut',
      },
    },
  };

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'Fixed Income':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Standard':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Growth':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 100) return 'bg-green-500';
    if (percentage >= 75) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-orange-500';
  };

  const formatDeadline = (deadline: Date) => {
    return new Intl.DateTimeFormat(currentLocale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(deadline));
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      whileTap="tap"
      className="cursor-pointer"
      onClick={() => onClick(goal)}
    >
      <Card className="relative overflow-hidden bg-gradient-to-br from-white to-gray-50 border-2 hover:border-orange-200 transition-colors duration-200 rounded-3xl shadow-lg hover:shadow-xl">
        {/* Nest Pattern Background */}
        <div className="absolute top-0 right-0 w-32 h-32 opacity-5">
          <div className="w-full h-full rounded-full bg-gradient-to-br from-orange-400 to-amber-500 transform rotate-12"></div>
        </div>

        <CardHeader className="pb-3 relative">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <motion.div
                variants={eggVariants}
                whileHover="bounce"
                className="p-2 bg-gradient-to-br from-orange-100 to-amber-100 rounded-full"
              >
                <Egg className="h-6 w-6 text-orange-600" />
              </motion.div>
              <div>
                <CardTitle className="text-lg font-bold text-gray-900">
                  {goal.name}
                </CardTitle>
                {goal.description && (
                  <p className="text-sm text-gray-600 mt-1">
                    {goal.description}
                  </p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {goal.isActive && (
                <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                  {t('goals.active')}
                </Badge>
              )}
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(goal);
                  }}
                  className="h-8 w-8 p-0 hover:bg-blue-100"
                  aria-label={`${t('common.edit')} ${goal.name}`}
                >
                  <Edit className="h-4 w-4 text-blue-600" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(goal.id);
                  }}
                  className="h-8 w-8 p-0 hover:bg-red-100"
                  aria-label={`${t('common.delete')} ${goal.name}`}
                >
                  <Trash2 className="h-4 w-4 text-red-600" />
                </Button>
              </div>
            </div>
          </div>
          <Badge className={`w-fit ${getStrategyColor(goal.strategy)}`} variant="secondary">
            {t(`goals.strategies.${goal.strategy.toLowerCase().replace(' ', '')}`)}
          </Badge>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Progress Section */}
          <div className="space-y-3">
            <div className="flex justify-between items-center text-sm">
              <span className="font-medium text-gray-700">{t('goals.progress')}</span>
              <span className="font-bold text-gray-900">{progressPercentage.toFixed(1)}%</span>
            </div>
            <div className="relative">
              <Progress 
                value={progressPercentage} 
                className="h-3 bg-gray-200 rounded-full overflow-hidden"
              />
              <div 
                className={`absolute top-0 left-0 h-3 rounded-full ${getProgressColor(progressPercentage)} transition-all duration-500`}
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>

          {/* Financial Details */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl">
              <div className="flex items-center justify-center gap-1 text-green-600 mb-1">
                <TrendingUp className="h-4 w-4" />
                <span className="text-xs font-medium">{t('goals.saved')}</span>
              </div>
              <div className="text-lg font-bold text-green-800">
                {formatCurrency(goal.saved, currentLocale)}
              </div>
            </div>

            <div className="text-center p-3 bg-gradient-to-br from-orange-50 to-amber-50 rounded-2xl">
              <div className="flex items-center justify-center gap-1 text-orange-600 mb-1">
                <Target className="h-4 w-4" />
                <span className="text-xs font-medium">{t('goals.remaining')}</span>
              </div>
              <div className="text-lg font-bold text-orange-800">
                {formatCurrency(remaining, currentLocale)}
              </div>
            </div>
          </div>

          {/* Portfolio Allocation */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">{t('goals.allocation')}</h4>
            <div className="flex gap-2 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span>{t('goals.equities')}: {allocation.equities}%</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>{t('goals.bonds')}: {allocation.bonds}%</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                <span>{t('goals.cash')}: {allocation.cash}%</span>
              </div>
            </div>
          </div>

          {/* Target and Deadline */}
          <div className="pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-1 text-gray-600">
                <Calendar className="h-4 w-4" />
                <span>{formatDeadline(goal.deadline)}</span>
              </div>
              <div className="text-xl font-bold text-gray-900">
                {formatCurrency(goal.target, currentLocale)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
