'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Textarea } from '../ui/textarea';
import { useTranslation, formatCurrency } from '../../lib/i18n';
import { Goal, InvestmentStrategy } from '../../lib/types';
import { investmentStrategies, portfolioAllocations } from '../../lib/constants';
import { Target, Calendar, DollarSign, TrendingUp, Egg, Info } from 'lucide-react';

interface EnhancedGoalFormProps {
  goal?: Goal;
  onSubmit: (goalData: any) => void;
  onCancel: () => void;
}

export function EnhancedGoalForm({ goal, onSubmit, onCancel }: EnhancedGoalFormProps) {
  const { t, currentLocale } = useTranslation();
  const [formData, setFormData] = useState({
    name: goal?.name || '',
    target: goal?.target || 0,
    deadline: goal?.deadline ? new Date(goal.deadline).toISOString().split('T')[0] : '',
    strategy: goal?.strategy || 'Standard' as InvestmentStrategy,
    description: goal?.description || '',
    timeHorizon: goal?.timeHorizon || 5,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 10,
      },
    },
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('validation.required');
    }

    if (formData.target <= 0) {
      newErrors.target = t('validation.positiveNumber');
    }

    if (!formData.deadline) {
      newErrors.deadline = t('validation.required');
    } else {
      const deadlineDate = new Date(formData.deadline);
      const today = new Date();
      if (deadlineDate <= today) {
        newErrors.deadline = t('validation.futureDate');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const allocation = portfolioAllocations[formData.strategy];
    
    onSubmit({
      ...formData,
      target: Number(formData.target),
      deadline: new Date(formData.deadline),
      allocation,
      isActive: true,
    });
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const selectedAllocation = portfolioAllocations[formData.strategy];
  const strategyDetails = investmentStrategies[formData.strategy];

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="max-w-4xl mx-auto"
    >
      <Card className="rounded-3xl shadow-2xl bg-white overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-orange-500 to-amber-500 text-white">
          <div className="flex items-center gap-3">
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ type: 'spring', stiffness: 200, damping: 10 }}
              className="p-3 bg-white/20 rounded-full"
            >
              <Egg className="h-8 w-8" />
            </motion.div>
            <div>
              <CardTitle className="text-2xl font-bold">
                {goal ? t('goals.editGoal') : t('goals.createNewNest')}
              </CardTitle>
              <p className="text-orange-100 mt-1">
                {goal ? t('goals.editSubtitle') : t('goals.createSubtitle')}
              </p>
            </div>
          </div>
        </CardHeader>

        <form onSubmit={handleSubmit}>
          <CardContent className="p-8 space-y-8">
            {/* Basic Information */}
            <motion.div variants={itemVariants} className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Target className="h-5 w-5 text-orange-600" />
                {t('goals.basicInformation')}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-medium">
                    {t('goals.goalName')} *
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder={t('goals.namePlaceholder')}
                    className={`rounded-full ${errors.name ? 'border-red-500' : ''}`}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="target" className="text-sm font-medium">
                    {t('goals.targetAmount')} *
                  </Label>
                  <Input
                    id="target"
                    type="number"
                    value={formData.target}
                    onChange={(e) => handleInputChange('target', e.target.value)}
                    placeholder="10000"
                    className={`rounded-full ${errors.target ? 'border-red-500' : ''}`}
                  />
                  {errors.target && (
                    <p className="text-sm text-red-600">{errors.target}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="deadline" className="text-sm font-medium">
                    {t('goals.deadline')} *
                  </Label>
                  <Input
                    id="deadline"
                    type="date"
                    value={formData.deadline}
                    onChange={(e) => handleInputChange('deadline', e.target.value)}
                    className={`rounded-full ${errors.deadline ? 'border-red-500' : ''}`}
                  />
                  {errors.deadline && (
                    <p className="text-sm text-red-600">{errors.deadline}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timeHorizon" className="text-sm font-medium">
                    {t('goals.timeHorizon')}
                  </Label>
                  <Input
                    id="timeHorizon"
                    type="number"
                    value={formData.timeHorizon}
                    onChange={(e) => handleInputChange('timeHorizon', Number(e.target.value))}
                    placeholder="5"
                    className="rounded-full"
                    min="1"
                    max="50"
                  />
                  <p className="text-xs text-gray-500">{t('goals.timeHorizonHelp')}</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium">
                  {t('goals.description')}
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={t('goals.descriptionPlaceholder')}
                  className="rounded-2xl resize-none"
                  rows={3}
                />
              </div>
            </motion.div>

            {/* Investment Strategy */}
            <motion.div variants={itemVariants} className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                {t('goals.investmentStrategy')}
              </h3>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    {t('goals.strategy')}
                  </Label>
                  <Select
                    value={formData.strategy}
                    onValueChange={(value: InvestmentStrategy) => handleInputChange('strategy', value)}
                  >
                    <SelectTrigger className="rounded-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(investmentStrategies).map(([key, strategy]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full bg-current"
                              style={{ color: strategy.color.replace('text-', '') }}
                            />
                            {key}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Strategy Details */}
                <div className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="space-y-2">
                      <h4 className="font-medium text-blue-900">{formData.strategy}</h4>
                      <p className="text-sm text-blue-700">{strategyDetails.description}</p>
                      <div className="flex gap-4 text-xs text-blue-600">
                        <span><strong>{t('goals.risk')}:</strong> {strategyDetails.risk}</span>
                        <span><strong>{t('goals.expectedReturn')}:</strong> {strategyDetails.return}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Portfolio Allocation */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-700">
                    {t('goals.portfolioAllocation')}
                  </h4>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-xl">
                      <div className="w-4 h-4 bg-blue-500 rounded-full mx-auto mb-2"></div>
                      <div className="text-sm font-medium text-blue-900">
                        {t('goals.equities')}
                      </div>
                      <div className="text-lg font-bold text-blue-800">
                        {selectedAllocation.equities}%
                      </div>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-xl">
                      <div className="w-4 h-4 bg-green-500 rounded-full mx-auto mb-2"></div>
                      <div className="text-sm font-medium text-green-900">
                        {t('goals.bonds')}
                      </div>
                      <div className="text-lg font-bold text-green-800">
                        {selectedAllocation.bonds}%
                      </div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-xl">
                      <div className="w-4 h-4 bg-gray-400 rounded-full mx-auto mb-2"></div>
                      <div className="text-sm font-medium text-gray-900">
                        {t('goals.cash')}
                      </div>
                      <div className="text-lg font-bold text-gray-800">
                        {selectedAllocation.cash}%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div variants={itemVariants} className="flex gap-4 pt-6">
              <Button
                type="submit"
                className="flex-1 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white rounded-full py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
              >
                {goal ? t('goals.updateGoal') : t('goals.createGoal')}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="flex-1 rounded-full py-3 text-lg font-semibold"
              >
                {t('common.cancel')}
              </Button>
            </motion.div>
          </CardContent>
        </form>
      </Card>
    </motion.div>
  );
}
