'use client';

import { useState, useEffect } from 'react';
import { Dashboard } from '../dashboard/dashboard';
import { CardForm } from '../cards/card-form';
import { CardList } from '../cards/card-list';
import { GoalForm } from '../goals/goal-form';
import { GoalList } from '../goals/goal-list';
import { ManageGoals } from '../goals/manage-goals';
import { BankAccounts } from '../accounts/bank-accounts';
import { RetirementAccounts } from '../accounts/retirement-accounts';
import { CompoundingCalculator } from '../calculator/compounding-calculator';
import { PortfolioSummary } from '../portfolio/portfolio-summary';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { useTranslation } from '../../lib/i18n';
import {
  BankCard,
  Goal,
  Transaction,
  SavingSettings,
  User,
  Screen,
  CardFormData,
  GoalFormData,
  InvestmentStrategy,
  InvestmentStrategyDetails,
  BankAccount,
  RetirementAccount,
  PortfolioSummary as PortfolioSummaryType,
} from '../../lib/types';
import { mockBankAccounts, mockRetirementAccounts, portfolioAllocations } from '../../lib/constants';
import { Coffee, ShoppingBag, Car, Home, Utensils, ArrowLeft } from 'lucide-react';

// Mock data
const mockUser: User = {
  email: '<EMAIL>',
  name: 'John Doe',
};

const mockCards: BankCard[] = [
  {
    id: '1',
    name: 'Main Checking',
    type: 'Debit',
    cardNumber: '****************',
    cvv: '123',
    expirationDate: '12/25',
    lastFour: '9012',
  },
  {
    id: '2',
    name: 'Travel Rewards',
    type: 'Credit',
    cardNumber: '****************',
    cvv: '456',
    expirationDate: '08/26',
    lastFour: '2222',
    limit: 5000,
  },
];

const mockGoals: Goal[] = [
  {
    id: '1',
    name: 'Emergency Fund',
    target: 10000,
    deadline: new Date('2024-12-31'),
    saved: 3500,
    strategy: 'Fixed Income',
    isActive: true,
  },
  {
    id: '2',
    name: 'Vacation',
    target: 5000,
    deadline: new Date('2024-08-15'),
    saved: 1200,
    strategy: 'Standard',
    isActive: false,
  },
];

const mockTransactions: Transaction[] = [
  {
    id: '1',
    merchant: 'Coffee Shop',
    amount: 4.5,
    savings: 0.5,
    method: 'roundups',
    icon: Coffee,
    time: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    merchant: 'Grocery Store',
    amount: 67.89,
    savings: 2.11,
    method: 'percentage',
    icon: ShoppingBag,
    time: '2024-01-15T14:20:00Z',
  },
  {
    id: '3',
    merchant: 'Gas Station',
    amount: 45.0,
    savings: 5.0,
    method: 'recurring',
    icon: Car,
    time: '2024-01-14T18:45:00Z',
  },
];

const mockSavingSettings: SavingSettings = {
  roundups: true,
  percentage: true,
  percentageValue: 3,
  recurring: true,
  recurringAmount: 100,
  recurringFrequency: 'weekly',
};

const investmentStrategies: Record<InvestmentStrategy, InvestmentStrategyDetails> = {
  'Fixed Income': {
    risk: 'Low Risk',
    return: '3-5% annually',
    description: 'Conservative approach with stable returns',
    projection: 'Safe and steady growth',
    color: 'bg-blue-100 text-blue-800',
  },
  Standard: {
    risk: 'Medium Risk',
    return: '6-8% annually',
    description: 'Balanced portfolio with moderate growth',
    projection: 'Balanced risk and reward',
    color: 'bg-green-100 text-green-800',
  },
  Growth: {
    risk: 'High Risk',
    return: '9-12% annually',
    description: 'Aggressive strategy for maximum returns',
    projection: 'High potential returns',
    color: 'bg-purple-100 text-purple-800',
  },
};

export function FintechApp() {
  const { t } = useTranslation();
  const [currentScreen, setCurrentScreen] = useState<Screen>('dashboard');
  const [user, setUser] = useState<User | null>(mockUser);
  const [cards, setCards] = useState<BankCard[]>(mockCards);
  const [goals, setGoals] = useState<Goal[]>(mockGoals);
  const [transactions, setTransactions] = useState<Transaction[]>(mockTransactions);
  const [savingSettings, setSavingSettings] = useState<SavingSettings>(mockSavingSettings);
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>(mockBankAccounts);
  const [retirementAccounts, setRetirementAccounts] = useState<RetirementAccount[]>(mockRetirementAccounts);

  // Calculate totals
  const totalSavings = goals.reduce((sum, goal) => sum + goal.saved, 0);
  const monthlyContribution = 450; // Mock value
  const activeGoals = goals.filter(goal => goal.isActive);

  // Portfolio summary calculations
  const portfolioSummary: PortfolioSummaryType = {
    cashTotal: bankAccounts.reduce((sum, account) => sum + account.balance, 0),
    investmentTotal: 25420.75, // Mock investment value
    nestsTotal: totalSavings,
    totalBalance: bankAccounts.reduce((sum, account) => sum + account.balance, 0) + 25420.75 + totalSavings,
    monthlyGrowth: 2.3, // Mock growth percentage
    ytdGrowth: 8.7, // Mock YTD growth
    sparklineData: [100, 102, 98, 105, 110, 108, 115, 112, 118, 120], // Mock sparkline data
  };

  const handleAddCard = (cardData: CardFormData) => {
    const newCard: BankCard = {
      id: Date.now().toString(),
      name: cardData.name,
      type: cardData.type,
      cardNumber: cardData.cardNumber,
      cvv: cardData.cvv,
      expirationDate: cardData.expirationDate,
      lastFour: cardData.cardNumber.slice(-4),
      limit: cardData.limit ? parseFloat(cardData.limit) : undefined,
    };
    setCards(prev => [...prev, newCard]);
    setCurrentScreen('dashboard');
  };

  const handleRemoveCard = (cardId: string) => {
    setCards(prev => prev.filter(card => card.id !== cardId));
  };

  const handleAddGoal = (goalData: GoalFormData & { strategy: InvestmentStrategy }) => {
    const newGoal: Goal = {
      id: Date.now().toString(),
      name: goalData.name,
      target: parseFloat(goalData.target),
      deadline: new Date(goalData.deadline),
      saved: 0,
      strategy: goalData.strategy,
      isActive: false,
    };
    setGoals(prev => [...prev, newGoal]);
    setCurrentScreen('dashboard');
  };

  const handleEditGoal = (goal: Goal) => {
    // In a real app, this would open an edit form
    console.log('Edit goal:', goal);
  };

  const handleDeleteGoal = (goalId: string) => {
    setGoals(prev => prev.filter(goal => goal.id !== goalId));
  };

  const handleAddBankAccount = () => {
    // In a real app, this would integrate with Plaid
    const newAccount: BankAccount = {
      id: Date.now().toString(),
      name: 'New Account',
      bankName: 'Sample Bank',
      accountType: 'checking',
      lastFour: '0000',
      balance: 0,
      isLinked: false,
    };
    setBankAccounts(prev => [...prev, newAccount]);
  };

  const handleRemoveBankAccount = (accountId: string) => {
    setBankAccounts(prev => prev.filter(account => account.id !== accountId));
  };

  const handleAddRetirementAccount = (accountData: any) => {
    const newAccount: RetirementAccount = {
      id: Date.now().toString(),
      name: `${accountData.type} Account`,
      type: accountData.type,
      country: accountData.country,
      balance: 0,
      contributionLimit: accountData.contributionLimit,
      currentYearContribution: 0,
    };
    setRetirementAccounts(prev => [...prev, newAccount]);
  };

  const handleLogout = () => {
    setUser(null);
    setCurrentScreen('login');
  };

  const renderScreen = () => {
    switch (currentScreen) {
      case 'dashboard':
        return (
          <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <PortfolioSummary summary={portfolioSummary} />
              <Dashboard
                totalSavings={totalSavings}
                monthlyContribution={monthlyContribution}
                activeGoals={activeGoals}
                recentTransactions={transactions}
                savingSettings={savingSettings}
                onAddCard={() => setCurrentScreen('add-card')}
                onAddGoal={() => setCurrentScreen('add-goal')}
                onViewInvestments={() => setCurrentScreen('investments')}
                onViewCards={() => setCurrentScreen('add-card')}
                onViewGoals={() => setCurrentScreen('manage-goals')}
                onViewBankAccounts={() => setCurrentScreen('bank-accounts')}
                onViewRetirementAccounts={() => setCurrentScreen('retirement-accounts')}
                onViewCalculator={() => setCurrentScreen('compounding-calculator')}
                onViewProfile={() => setCurrentScreen('profile')}
                onLogout={handleLogout}
              />
            </div>
          </div>
        );

      case 'add-card':
        return (
          <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="mb-6">
                <Button
                  variant="ghost"
                  onClick={() => setCurrentScreen('dashboard')}
                  className="gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  {t('common.back')}
                </Button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h1 className="text-2xl font-bold mb-6">{t('cards.title')}</h1>
                  <CardForm
                    onSubmit={handleAddCard}
                    onCancel={() => setCurrentScreen('dashboard')}
                  />
                </div>

                <div>
                  <h2 className="text-xl font-semibold mb-4">Your Cards</h2>
                  <CardList cards={cards} onRemoveCard={handleRemoveCard} />
                </div>
              </div>
            </div>
          </div>
        );

      case 'add-goal':
        return (
          <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="mb-6">
                <Button
                  variant="ghost"
                  onClick={() => setCurrentScreen('dashboard')}
                  className="gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  {t('common.back')}
                </Button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h1 className="text-2xl font-bold mb-6">{t('goals.title')}</h1>
                  <GoalForm
                    onSubmit={handleAddGoal}
                    onCancel={() => setCurrentScreen('dashboard')}
                  />
                </div>

                <div>
                  <h2 className="text-xl font-semibold mb-4">Your Goals</h2>
                  <GoalList goals={goals} onEditGoal={handleEditGoal} />
                </div>
              </div>
            </div>
          </div>
        );

      case 'investments':
        return (
          <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="mb-6">
                <Button
                  variant="ghost"
                  onClick={() => setCurrentScreen('dashboard')}
                  className="gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  {t('common.back')}
                </Button>
              </div>

              <h1 className="text-2xl font-bold mb-6">{t('investments.title')}</h1>

              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {Object.entries(investmentStrategies).map(([strategy, details]) => (
                  <Card key={strategy}>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        {t(`investments.${strategy.toLowerCase().replace(' ', '')}.name`)}
                        <span className={`px-2 py-1 rounded text-xs ${details.color}`}>
                          {t(`investments.${strategy.toLowerCase().replace(' ', '')}.risk`)}
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <p className="font-semibold text-green-600">
                          {t(`investments.${strategy.toLowerCase().replace(' ', '')}.return`)}
                        </p>
                        <p className="text-sm text-gray-600 mt-2">
                          {t(`investments.${strategy.toLowerCase().replace(' ', '')}.description`)}
                        </p>
                      </div>
                      <div className="pt-4 border-t">
                        <p className="text-sm font-medium">
                          {t(`investments.${strategy.toLowerCase().replace(' ', '')}.projection`)}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        );

      case 'manage-goals':
        return (
          <ManageGoals
            goals={goals}
            onAddGoal={handleAddGoal}
            onEditGoal={handleEditGoal}
            onDeleteGoal={handleDeleteGoal}
            onBack={() => setCurrentScreen('dashboard')}
          />
        );

      case 'bank-accounts':
        return (
          <BankAccounts
            accounts={bankAccounts}
            onAddAccount={handleAddBankAccount}
            onRemoveAccount={handleRemoveBankAccount}
            onBack={() => setCurrentScreen('dashboard')}
          />
        );

      case 'retirement-accounts':
        return (
          <RetirementAccounts
            accounts={retirementAccounts}
            onAddAccount={handleAddRetirementAccount}
            onBack={() => setCurrentScreen('dashboard')}
          />
        );

      case 'compounding-calculator':
        return (
          <CompoundingCalculator
            onBack={() => setCurrentScreen('dashboard')}
          />
        );

      default:
        return (
          <Dashboard
            totalSavings={totalSavings}
            monthlyContribution={monthlyContribution}
            activeGoals={activeGoals}
            recentTransactions={transactions}
            savingSettings={savingSettings}
            onAddCard={() => setCurrentScreen('add-card')}
            onAddGoal={() => setCurrentScreen('add-goal')}
            onViewInvestments={() => setCurrentScreen('investments')}
            onViewCards={() => setCurrentScreen('add-card')}
            onViewGoals={() => setCurrentScreen('add-goal')}
            onViewProfile={() => setCurrentScreen('profile')}
            onLogout={handleLogout}
          />
        );
    }
  };

  return renderScreen();
}
