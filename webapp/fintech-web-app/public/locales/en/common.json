{"app": {"title": "FinTech App", "subtitle": "Smart Savings & Investments"}, "navigation": {"dashboard": "Dashboard", "cards": "Cards", "goals": "Goals", "investments": "Investments", "profile": "Profile"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "email": "Email", "password": "Password", "loginButton": "Sign In", "welcome": "Welcome back!"}, "dashboard": {"totalSavings": "Total Savings", "monthlyContribution": "Monthly Contribution", "activeGoals": "Active Goals", "recentTransactions": "Recent Transactions", "quickActions": "Quick Actions", "addCard": "Add Card", "addGoal": "Add Goal", "viewInvestments": "View Investments", "bankAccounts": "Bank Accounts", "retirementAccounts": "Retirement Accounts", "calculator": "Compounding Calculator"}, "cards": {"title": "Payment Cards", "addNew": "Add New Card", "cardName": "Card Name", "cardNumber": "Card Number", "cvv": "CVV", "expirationDate": "Expiration Date", "cardType": "Card Type", "debit": "Debit", "credit": "Credit", "limit": "Credit Limit", "save": "Save Card", "cancel": "Cancel", "remove": "Remove Card", "removeConfirmation": "Are you sure you want to remove this card?", "lastFour": "•••• {{lastFour}}"}, "goals": {"title": "Savings Goals", "addNew": "Add New Goal", "goalName": "Goal Name", "targetAmount": "Target Amount", "deadline": "Deadline", "strategy": "Investment Strategy", "save": "Save Goal", "cancel": "Cancel", "progress": "Progress", "saved": "Saved", "remaining": "Remaining", "changeGoalConfirmation": "Are you sure you want to change this goal? It's currently running.", "active": "Active", "allocation": "Portfolio Allocation", "equities": "Equities", "bonds": "<PERSON><PERSON>", "cash": "Cash", "editGoal": "Edit Goal", "createNewNest": "Create New Nest", "editSubtitle": "Update your goal details", "createSubtitle": "Start building your financial future", "basicInformation": "Basic Information", "namePlaceholder": "e.g., Dream Vacation, Emergency Fund", "timeHorizon": "Time Horizon (years)", "timeHorizonHelp": "How many years do you plan to save for this goal?", "description": "Description", "descriptionPlaceholder": "Tell us more about this goal...", "investmentStrategy": "Investment Strategy", "risk": "Risk", "expectedReturn": "Expected Return", "portfolioAllocation": "Portfolio Allocation", "updateGoal": "Update Goal", "createGoal": "Create Goal", "strategies": {"fixedIncome": "Fixed Income", "standard": "Standard", "growth": "Growth"}, "deleteConfirmation": {"title": "Delete Goal", "description": "Are you sure you want to delete this goal? This action cannot be undone."}, "manage": {"title": "Manage Your Nests", "subtitle": "Create, edit, and track your financial goals", "searchPlaceholder": "Search goals...", "noGoalsTitle": "No goals yet", "noGoalsSubtitle": "Create your first nest to start saving for your dreams", "noResultsTitle": "No matching goals", "noResultsSubtitle": "Try adjusting your search or filter criteria", "filters": {"all": "All Goals", "active": "Active", "completed": "Completed"}}}, "investments": {"title": "Investment Strategies", "fixedIncome": {"name": "Fixed Income", "risk": "Low Risk", "return": "3-5% annually", "description": "Conservative approach with stable returns", "projection": "Safe and steady growth"}, "standard": {"name": "Standard", "risk": "Medium Risk", "return": "6-8% annually", "description": "Balanced portfolio with moderate growth", "projection": "Balanced risk and reward"}, "growth": {"name": "Growth", "risk": "High Risk", "return": "9-12% annually", "description": "Aggressive strategy for maximum returns", "projection": "High potential returns"}}, "savings": {"methods": "Saving Methods", "roundups": "Round-ups", "percentage": "Percentage of purchases", "recurring": "Recurring transfers", "enable": "Enable", "disable": "Disable", "amount": "Amount", "frequency": "Frequency"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "confirm": "Confirm", "back": "Back", "next": "Next", "loading": "Loading...", "error": "Error", "success": "Success", "currency": "$"}, "portfolio": {"summary": {"title": "Portfolio Overview", "subtitle": "Your complete financial picture", "totalBalance": "Total Balance", "cashTotal": "Cash Accounts", "investmentTotal": "Investments", "nestsTotal": "Goals & Nests", "monthlyGrowth": "Monthly", "ytdGrowth": "YTD", "thisMonth": "this month", "ytd": "YTD", "trend": "trend", "performanceTitle": "Performance Summary", "performanceSubtitle": "Track your financial growth over time"}}, "accounts": {"bank": {"title": "Bank Accounts", "subtitle": "Manage your linked bank accounts", "linkNew": "<PERSON> Account", "totalBalance": "Total Balance", "accountsCount": "{{count}} linked accounts", "noAccountsTitle": "No bank accounts linked", "noAccountsSubtitle": "Connect your bank accounts to get started", "linkFirst": "<PERSON> Your First Account", "linked": "Linked", "pending": "Pending", "balance": "Balance", "accountNumber": "Account Number", "linkedDate": "Linked Date", "remove": "Remove Account", "types": {"checking": "Checking", "savings": "Savings"}, "removeConfirmation": {"title": "Remove Bank Account", "description": "Are you sure you want to remove this bank account? This action cannot be undone."}}, "retirement": {"title": "Retirement Accounts", "subtitle": "Plan for your future with tax-advantaged accounts", "addNew": "Add Retirement Account", "totalBalance": "Total Balance", "thisYearContributions": "This Year Contributions", "remainingLimit": "Remaining Limit", "noAccountsTitle": "No retirement accounts", "noAccountsSubtitle": "Start planning for your future with tax-advantaged retirement accounts", "createFirst": "Create Your First Account", "selectCountry": "Select Country", "availableTypes": "Available Account Types", "annualLimit": "Annual Limit", "create": "Create Account", "currentBalance": "Current Balance", "contributionProgress": "Contribution Progress", "remainingContribution": "Remaining Contribution"}}, "calculator": {"title": "Compounding Calculator", "subtitle": "See how your money can grow over time", "inputs": {"title": "Investment Parameters", "initialAmount": "Initial Amount", "monthlyContribution": "Monthly Contribution", "interestRate": "Annual Interest Rate", "years": "Investment Period", "yearsUnit": "years"}, "results": {"title": "Projected Results", "finalBalance": "Final Balance", "totalContributions": "Total Contributions", "interestEarned": "Interest Earned"}, "animation": {"title": "Interest Growth Visualization", "subtitle": "Each coffee cup represents $100 of interest earned", "year": "Year"}, "breakdown": {"title": "Year-by-Year Breakdown", "year": "Year", "balance": "Balance", "contributions": "Contributions", "interest": "Interest"}, "chart": {"title": "Investment Growth Chart", "subtitle": "Visual representation of your investment growth over time", "totalInvested": "Total Invested", "compoundValue": "Compound Value", "year": "Year"}, "compound": {"title": "Compound Interest Calculator", "description": "See how your investments can grow with compound interest over time", "feature1": "Interactive charts and projections", "feature2": "Real-time calculations", "feature3": "Year-by-year breakdown"}, "inflation": {"title": "Inflation Calculator", "subtitle": "Understand how inflation affects your purchasing power over time", "description": "Calculate the real value of money across different time periods and countries", "initialAmount": "Initial Amount", "country": "Country", "startYear": "Start Year", "endYear": "End Year", "explanation": {"title": "How it works", "description": "This calculator shows how {{amount}} in {{startYear}} compares to today's purchasing power in {{endYear}} in {{country}}, based on historical inflation data."}, "results": {"title": "Inflation Impact", "originalValue": "Original Value", "adjustedValue": "Today's Value", "totalInflation": "Total Inflation", "purchasingPower": "purchasing power", "valueDecline": "value decline"}, "chart": {"title": "Purchasing Power Over Time", "subtitle": "How your money's value changes due to inflation", "purchasingPower": "Purchasing Power", "year": "Year"}, "feature1": "Multi-country inflation data", "feature2": "Historical CPI analysis", "feature3": "Visual purchasing power trends"}, "dashboard": {"title": "Financial Calculators", "subtitle": "Powerful tools to help you plan and understand your financial future", "features": "Features", "startCalculating": "Start Calculating", "education": {"title": "Understanding Financial Concepts", "compoundInterest": "Compound Interest", "compoundDescription": "The power of earning returns on your returns - often called the eighth wonder of the world.", "inflation": "Inflation", "inflationDescription": "How the general increase in prices affects your money's purchasing power over time."}}}, "education": {"hero": {"title": "Learn to Grow Your Nest", "subtitle": "Discover financial wisdom to build your wealth"}, "searchPlaceholder": "Search articles...", "filters": {"all": "All Topics"}, "featured": "Featured", "allArticles": "All Articles", "backToArticles": "Back to Articles", "shareArticle": "Share this article", "shareDescription": "Help others learn by sharing this knowledge", "share": {"twitter": "Twitter", "linkedin": "LinkedIn", "copy": "Copy Link"}, "noResults": {"title": "No articles found", "subtitle": "Try adjusting your search terms or filters", "clearFilters": "Clear Filters"}}, "validation": {"required": "This field is required", "positiveNumber": "Must be a positive number", "futureDate": "Date must be in the future", "cardName": {"required": "Card name is required"}, "cardNumber": {"invalid": "Invalid card number"}, "cvv": {"invalid": "Invalid CVV"}, "expirationDate": {"invalid": "Invalid expiration date"}, "goalName": {"required": "Goal name is required"}, "target": {"invalid": "Target amount must be greater than 0"}, "deadline": {"future": "Deadline must be in the future"}, "email": {"invalid": "Invalid email address"}, "password": {"minLength": "Password must be at least 6 characters"}}}