'use client';

import { useState } from 'react';
import { Layout } from '@/components/layout/layout';
import { PortfolioSummary } from '@/components/portfolio/portfolio-summary';
import { Dashboard } from '@/components/dashboard/dashboard';
import { mockGoals, mockTransactions, mockSavingSettings, mockUser } from '@/lib/mock-data';
import { mockBankAccounts, mockRetirementAccounts } from '@/lib/constants';
import { PortfolioSummary as PortfolioSummaryType } from '@/lib/types';

export default function FintechPage() {
  const [goals] = useState(mockGoals);
  const [transactions] = useState(mockTransactions);
  const [savingSettings] = useState(mockSavingSettings);
  const [user] = useState(mockUser);

  // Calculate totals
  const totalSavings = goals.reduce((sum, goal) => sum + goal.saved, 0);
  const monthlyContribution = 450; // Mock value
  const activeGoals = goals.filter(goal => goal.isActive);

  // Portfolio summary calculations
  const portfolioSummary: PortfolioSummaryType = {
    cashTotal: mockBankAccounts.reduce((sum, account) => sum + account.balance, 0),
    investmentTotal: 25420.75, // Mock investment value
    nestsTotal: totalSavings,
    totalBalance: mockBankAccounts.reduce((sum, account) => sum + account.balance, 0) + 25420.75 + totalSavings,
    monthlyGrowth: 2.3, // Mock growth percentage
    ytdGrowth: 8.7, // Mock YTD growth
    sparklineData: [100, 102, 98, 105, 110, 108, 115, 112, 118, 120], // Mock sparkline data
  };

  const handleLogout = () => {
    // TODO: Implement logout logic
    console.log('Logout clicked');
  };

  return (
    <Layout title="Dashboard">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <PortfolioSummary summary={portfolioSummary} />
        <Dashboard
          totalSavings={totalSavings}
          monthlyContribution={monthlyContribution}
          activeGoals={activeGoals}
          recentTransactions={transactions}
          savingSettings={savingSettings}
          onAddCard={() => console.log('Add card')}
          onAddGoal={() => window.location.href = '/fintech/goals'}
          onViewInvestments={() => console.log('View investments')}
          onViewCards={() => console.log('View cards')}
          onViewGoals={() => window.location.href = '/fintech/goals'}
          onViewBankAccounts={() => window.location.href = '/fintech/accounts'}
          onViewRetirementAccounts={() => window.location.href = '/fintech/accounts'}
          onViewCalculator={() => window.location.href = '/fintech/calculator'}
          onViewProfile={() => console.log('View profile')}
          onLogout={handleLogout}
        />
      </div>
    </Layout>
  );
}
