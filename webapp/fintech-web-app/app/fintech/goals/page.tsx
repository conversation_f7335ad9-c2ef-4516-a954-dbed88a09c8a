'use client';

import { useState } from 'react';
import { ManageGoals } from '@/components/goals/manage-goals';
import { Layout } from '@/components/layout/layout';
import { mockGoals } from '@/lib/mock-data';
import { Goal } from '@/lib/types';

export default function GoalsPage() {
  const [goals, setGoals] = useState<Goal[]>(mockGoals);

  const handleAddGoal = (goalData: any) => {
    const newGoal: Goal = {
      id: Date.now().toString(),
      ...goalData,
      saved: 0,
      isActive: true,
    };
    setGoals(prev => [...prev, newGoal]);
  };

  const handleEditGoal = (updatedGoal: Goal) => {
    setGoals(prev => prev.map(goal => 
      goal.id === updatedGoal.id ? updatedGoal : goal
    ));
  };

  const handleDeleteGoal = (goalId: string) => {
    setGoals(prev => prev.filter(goal => goal.id !== goalId));
  };

  return (
    <Layout title="Manage Goals">
      <ManageGoals
        goals={goals}
        onAddGoal={handleAddGoal}
        onEditGoal={handleEditGoal}
        onDeleteGoal={handleDeleteGoal}
        onBack={() => window.history.back()}
      />
    </Layout>
  );
}
