'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { BankAccounts } from '@/components/accounts/bank-accounts';
import { RetirementAccounts } from '@/components/accounts/retirement-accounts';
import { Layout } from '@/components/layout/layout';
import { mockBankAccounts, mockRetirementAccounts } from '@/lib/constants';
import { BankAccount, RetirementAccount } from '@/lib/types';
import { Building2, PiggyBank, ArrowRight } from 'lucide-react';

type AccountView = 'overview' | 'bank' | 'retirement';

export default function AccountsPage() {
  const [currentView, setCurrentView] = useState<AccountView>('overview');
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>(mockBankAccounts);
  const [retirementAccounts, setRetirementAccounts] = useState<RetirementAccount[]>(mockRetirementAccounts);

  const handleAddBankAccount = () => {
    const newAccount: BankAccount = {
      id: Date.now().toString(),
      name: 'New Account',
      bankName: 'Sample Bank',
      accountType: 'checking',
      lastFour: '0000',
      balance: 0,
      isLinked: false,
    };
    setBankAccounts(prev => [...prev, newAccount]);
  };

  const handleRemoveBankAccount = (accountId: string) => {
    setBankAccounts(prev => prev.filter(account => account.id !== accountId));
  };

  const handleAddRetirementAccount = (accountData: any) => {
    const newAccount: RetirementAccount = {
      id: Date.now().toString(),
      name: `${accountData.type} Account`,
      type: accountData.type,
      country: accountData.country,
      balance: 0,
      contributionLimit: accountData.contributionLimit,
      currentYearContribution: 0,
    };
    setRetirementAccounts(prev => [...prev, newAccount]);
  };

  if (currentView === 'bank') {
    return (
      <BankAccounts
        accounts={bankAccounts}
        onAddAccount={handleAddBankAccount}
        onRemoveAccount={handleRemoveBankAccount}
        onBack={() => setCurrentView('overview')}
      />
    );
  }

  if (currentView === 'retirement') {
    return (
      <RetirementAccounts
        accounts={retirementAccounts}
        onAddAccount={handleAddRetirementAccount}
        onBack={() => setCurrentView('overview')}
      />
    );
  }

  return (
    <Layout title="Account Management">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-8"
        >
          {/* Header */}
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Account Management
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Manage your bank accounts and retirement savings in one place
            </p>
          </div>

          {/* Account Type Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Bank Accounts */}
            <Card 
              className="rounded-3xl shadow-xl bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 hover:shadow-2xl transition-all duration-300 cursor-pointer group"
              onClick={() => setCurrentView('bank')}
            >
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full group-hover:scale-110 transition-transform duration-200">
                    <Building2 className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-xl text-blue-900">
                    Bank Accounts
                  </CardTitle>
                </div>
                <p className="text-blue-700">
                  Link and manage your checking and savings accounts
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-blue-700">Linked Accounts:</span>
                    <span className="font-semibold text-blue-900">{bankAccounts.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-blue-700">Total Balance:</span>
                    <span className="font-semibold text-blue-900">
                      ${bankAccounts.reduce((sum, acc) => sum + acc.balance, 0).toLocaleString()}
                    </span>
                  </div>
                </div>
                <Button 
                  className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-full py-3 font-semibold group-hover:scale-105 transition-transform duration-200"
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentView('bank');
                  }}
                >
                  Manage Bank Accounts
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>

            {/* Retirement Accounts */}
            <Card 
              className="rounded-3xl shadow-xl bg-gradient-to-br from-purple-50 to-violet-50 border-purple-200 hover:shadow-2xl transition-all duration-300 cursor-pointer group"
              onClick={() => setCurrentView('retirement')}
            >
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-3 bg-gradient-to-br from-purple-500 to-violet-600 rounded-full group-hover:scale-110 transition-transform duration-200">
                    <PiggyBank className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-xl text-purple-900">
                    Retirement Accounts
                  </CardTitle>
                </div>
                <p className="text-purple-700">
                  Plan for your future with tax-advantaged accounts
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-purple-700">Active Accounts:</span>
                    <span className="font-semibold text-purple-900">{retirementAccounts.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-purple-700">Total Balance:</span>
                    <span className="font-semibold text-purple-900">
                      ${retirementAccounts.reduce((sum, acc) => sum + acc.balance, 0).toLocaleString()}
                    </span>
                  </div>
                </div>
                <Button 
                  className="w-full bg-gradient-to-r from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 text-white rounded-full py-3 font-semibold group-hover:scale-105 transition-transform duration-200"
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentView('retirement');
                  }}
                >
                  Manage Retirement Accounts
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </motion.div>
      </div>
    </Layout>
  );
}
