'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Shield, Zap } from 'lucide-react';
import DevLoginForm from '@/app/components/auth/DevLoginForm';

interface Props {
  onBack: () => void;
  onSuccess?: () => void;
}

export default function DevLoginScreen({ onBack, onSuccess }: Props) {
  const [showDevLogin, setShowDevLogin] = useState(false);

  const handleAuth0Login = () => {
    window.location.href = '/api/auth/login';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e58a35] to-amber-600 flex flex-col">
      <div className="p-6">
        <Button
          variant="ghost"
          onClick={onBack}
          className="text-white hover:bg-white/20"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Back
        </Button>
      </div>

      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-md space-y-6">
          <div className="text-center text-white space-y-4">
            <div className="w-16 h-16 mx-auto bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <Shield className="w-8 h-8" />
            </div>
            <h1 className="text-3xl font-bold">Welcome Back</h1>
            <p className="text-lg opacity-90">Choose how you&apos;d like to sign in</p>
          </div>

          {!showDevLogin ? (
            <div className="space-y-4">
              {/* Auth0 Login Option */}
              <Card className="bg-white/95 backdrop-blur-sm">
                <CardHeader className="text-center pb-3">
                  <CardTitle className="flex items-center justify-center gap-2">
                    <Shield className="h-5 w-5 text-[#e58a35]" />
                    Secure Login
                  </CardTitle>
                  <CardDescription>
                    Login with Auth0 (Production Ready)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button 
                    onClick={handleAuth0Login}
                    className="w-full bg-[#e58a35] hover:bg-[#d17a2e] text-white"
                    size="lg"
                  >
                    Continue with Auth0
                  </Button>
                </CardContent>
              </Card>

              <div className="flex items-center gap-4">
                <Separator className="flex-1 bg-white/30" />
                <span className="text-white/80 text-sm">OR</span>
                <Separator className="flex-1 bg-white/30" />
              </div>

              {/* Development Login Option */}
              <Card className="bg-white/95 backdrop-blur-sm">
                <CardHeader className="text-center pb-3">
                  <CardTitle className="flex items-center justify-center gap-2">
                    <Zap className="h-5 w-5 text-amber-600" />
                    Development Mode
                  </CardTitle>
                  <CardDescription>
                    Quick login for local development
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button 
                    onClick={() => setShowDevLogin(true)}
                    variant="outline"
                    className="w-full border-amber-600 text-amber-600 hover:bg-amber-50"
                    size="lg"
                  >
                    Use Development Login
                  </Button>
                </CardContent>
              </Card>

              <div className="text-center">
                <p className="text-white/70 text-sm">
                  New to our app? Development mode allows any email/password
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <DevLoginForm onSuccess={onSuccess} />
              
              <div className="text-center">
                <Button
                  variant="ghost"
                  onClick={() => setShowDevLogin(false)}
                  className="text-white hover:bg-white/20"
                >
                  ← Back to login options
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
