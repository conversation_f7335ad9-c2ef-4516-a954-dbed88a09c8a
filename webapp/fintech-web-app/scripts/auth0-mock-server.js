#!/usr/bin/env node

/**
 * Simple Auth0 Mock Server for E2E Testing
 * This creates a minimal Auth0-compatible server for testing authentication flows
 */

const express = require('express');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const path = require('path');

const app = express();
const PORT = 3002;

// Mock configuration
const ISSUER = `http://localhost:${PORT}`;
const AUDIENCE = 'my-api';
const CLIENT_ID = 'my-client-id';
const SECRET = 'test-secret-key-for-development-only';

// In-memory user store for testing
const users = new Map();

// Pre-populate with test users
users.set('<EMAIL>', {
  email: '<EMAIL>',
  password: 'Test1234',
  name: 'Test User',
  given_name: 'Test',
  family_name: 'User',
  nickname: 'testuser',
  picture: 'https://via.placeholder.com/150',
  email_verified: true,
  sub: 'auth0|test-user-123'
});

users.set('<EMAIL>', {
  email: '<EMAIL>',
  password: 'E2E1234',
  name: 'E2E Test User',
  given_name: 'E2E',
  family_name: 'User',
  nickname: 'e2euser',
  picture: 'https://via.placeholder.com/150',
  email_verified: true,
  sub: 'auth0|e2e-user-456'
});

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Body:', req.body);
  }
  next();
});

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// OpenID Configuration endpoint
app.get('/.well-known/openid_configuration', (req, res) => {
  res.json({
    issuer: ISSUER,
    authorization_endpoint: `${ISSUER}/authorize`,
    token_endpoint: `${ISSUER}/oauth/token`,
    userinfo_endpoint: `${ISSUER}/userinfo`,
    jwks_uri: `${ISSUER}/.well-known/jwks.json`,
    response_types_supported: ['code', 'token', 'id_token'],
    subject_types_supported: ['public'],
    id_token_signing_alg_values_supported: ['HS256'],
    scopes_supported: ['openid', 'profile', 'email']
  });
});

// JWKS endpoint
app.get('/.well-known/jwks.json', (req, res) => {
  // For simplicity, we'll use HMAC (HS256) instead of RSA
  // In a real scenario, you'd use RSA keys
  res.json({
    keys: [{
      kty: 'oct',
      kid: 'test-key-1',
      use: 'sig',
      alg: 'HS256',
      k: Buffer.from(SECRET).toString('base64url')
    }]
  });
});

// Authorization endpoint - shows login form
app.get('/authorize', (req, res) => {
  const { client_id, response_type, redirect_uri, scope, state, error } = req.query;

  // Simple HTML login form with error handling
  const errorMessage = error ? `<div class="error">${error}</div>` : '';

  const loginForm = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Auth0 Mock - Login</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 400px; margin: 50px auto; padding: 20px; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; }
            input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
            button { width: 100%; padding: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .register-link { text-align: center; margin-top: 15px; }
            .register-link a { color: #007bff; text-decoration: none; }
            .error { background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 15px; }
            .alert-danger { background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin-bottom: 15px; }
            .text-red-500 { color: #dc3545; }
            .text-danger { color: #dc3545; }
        </style>
    </head>
    <body>
        <h2>Sign In</h2>
        ${errorMessage}
        <form method="POST" action="/login">
            <input type="hidden" name="client_id" value="${client_id}">
            <input type="hidden" name="response_type" value="${response_type}">
            <input type="hidden" name="redirect_uri" value="${redirect_uri}">
            <input type="hidden" name="scope" value="${scope}">
            <input type="hidden" name="state" value="${state}">

            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit">Sign In</button>
        </form>
        
        <div class="register-link">
            <a href="#" onclick="toggleRegister()">Need an account? Sign up</a>
        </div>
        
        <form id="registerForm" method="POST" action="/register" style="display: none;">
            <h2>Sign Up</h2>
            <input type="hidden" name="client_id" value="${client_id}">
            <input type="hidden" name="response_type" value="${response_type}">
            <input type="hidden" name="redirect_uri" value="${redirect_uri}">
            <input type="hidden" name="scope" value="${scope}">
            <input type="hidden" name="state" value="${state}">
            
            <div class="form-group">
                <label for="reg_email">Email:</label>
                <input type="email" id="reg_email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="reg_password">Password:</label>
                <input type="password" id="reg_password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="reg_name">Full Name:</label>
                <input type="text" id="reg_name" name="name" required>
            </div>
            
            <button type="submit">Sign Up</button>
        </form>
        
        <script>
            function toggleRegister() {
                const loginForm = document.querySelector('form[action="/login"]');
                const registerForm = document.getElementById('registerForm');
                const isRegisterVisible = registerForm.style.display !== 'none';
                
                if (isRegisterVisible) {
                    registerForm.style.display = 'none';
                    loginForm.style.display = 'block';
                } else {
                    registerForm.style.display = 'block';
                    loginForm.style.display = 'none';
                }
            }
        </script>
    </body>
    </html>
  `;
  
  res.send(loginForm);
});

// Login endpoint
app.post('/login', (req, res) => {
  const { email, password, client_id, response_type, redirect_uri, scope, state } = req.body;

  const user = users.get(email);
  if (!user || user.password !== password) {
    // Redirect back to login form with error message
    const errorMessage = !user ? 'User not found' : 'Invalid password';
    const redirectUrl = `/authorize?client_id=${client_id}&response_type=${response_type}&redirect_uri=${encodeURIComponent(redirect_uri)}&scope=${encodeURIComponent(scope)}&state=${state}&error=${encodeURIComponent(errorMessage)}`;
    return res.redirect(redirectUrl);
  }
  
  // Generate authorization code
  const code = crypto.randomBytes(32).toString('hex');
  
  // Store code temporarily (in real implementation, use Redis or similar)
  const codeData = {
    user,
    client_id,
    redirect_uri,
    scope,
    expires: Date.now() + 600000 // 10 minutes
  };
  
  // Simple in-memory storage (not production-ready)
  global.authCodes = global.authCodes || new Map();
  global.authCodes.set(code, codeData);
  
  // Redirect back to application
  const redirectUrl = new URL(redirect_uri);
  redirectUrl.searchParams.set('code', code);
  if (state) redirectUrl.searchParams.set('state', state);
  
  res.redirect(redirectUrl.toString());
});

// Register endpoint
app.post('/register', (req, res) => {
  const { email, password, name, client_id, response_type, redirect_uri, scope, state } = req.body;
  
  if (users.has(email)) {
    return res.status(400).send('Email already registered');
  }
  
  // Create new user
  const nameParts = name.split(' ');
  const newUser = {
    email,
    password,
    name,
    given_name: nameParts[0] || name,
    family_name: nameParts.slice(1).join(' ') || '',
    nickname: email.split('@')[0],
    picture: 'https://via.placeholder.com/150',
    email_verified: true,
    sub: `auth0|${crypto.randomBytes(16).toString('hex')}`
  };
  
  users.set(email, newUser);
  
  // Generate authorization code
  const code = crypto.randomBytes(32).toString('hex');
  
  const codeData = {
    user: newUser,
    client_id,
    redirect_uri,
    scope,
    expires: Date.now() + 600000
  };
  
  global.authCodes = global.authCodes || new Map();
  global.authCodes.set(code, codeData);
  
  // Redirect back to application
  const redirectUrl = new URL(redirect_uri);
  redirectUrl.searchParams.set('code', code);
  if (state) redirectUrl.searchParams.set('state', state);
  
  res.redirect(redirectUrl.toString());
});

// Token endpoint
app.post('/oauth/token', (req, res) => {
  const { grant_type, code, client_id, redirect_uri } = req.body;
  
  if (grant_type !== 'authorization_code') {
    return res.status(400).json({ error: 'unsupported_grant_type' });
  }
  
  global.authCodes = global.authCodes || new Map();
  const codeData = global.authCodes.get(code);
  
  if (!codeData || codeData.expires < Date.now()) {
    return res.status(400).json({ error: 'invalid_grant' });
  }
  
  if (codeData.client_id !== client_id || codeData.redirect_uri !== redirect_uri) {
    return res.status(400).json({ error: 'invalid_grant' });
  }
  
  // Generate tokens
  const payload = {
    sub: codeData.user.sub,
    email: codeData.user.email,
    name: codeData.user.name,
    given_name: codeData.user.given_name,
    family_name: codeData.user.family_name,
    nickname: codeData.user.nickname,
    picture: codeData.user.picture,
    email_verified: codeData.user.email_verified,
    aud: AUDIENCE,
    iss: ISSUER,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour
  };
  
  const accessToken = jwt.sign(payload, SECRET, { algorithm: 'HS256' });
  const idToken = jwt.sign(payload, SECRET, { algorithm: 'HS256' });
  
  // Clean up used code
  global.authCodes.delete(code);
  
  res.json({
    access_token: accessToken,
    id_token: idToken,
    token_type: 'Bearer',
    expires_in: 3600
  });
});

// User info endpoint
app.get('/userinfo', (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'unauthorized' });
  }

  const token = authHeader.substring(7);

  try {
    const decoded = jwt.verify(token, SECRET);

    // Return user info based on the token
    res.json({
      sub: decoded.sub,
      email: decoded.email,
      name: decoded.name,
      given_name: decoded.given_name,
      family_name: decoded.family_name,
      nickname: decoded.nickname,
      picture: decoded.picture,
      email_verified: decoded.email_verified
    });
  } catch (error) {
    res.status(401).json({ error: 'invalid_token' });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'auth0-mock' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🔐 Auth0 Mock Server running on http://localhost:${PORT}`);
  console.log(`📋 OpenID Configuration: http://localhost:${PORT}/.well-known/openid_configuration`);
  console.log(`🔑 JWKS: http://localhost:${PORT}/.well-known/jwks.json`);
  console.log(`👤 Test users: <EMAIL> (Test1234), <EMAIL> (E2E1234)`);
});

module.exports = app;
