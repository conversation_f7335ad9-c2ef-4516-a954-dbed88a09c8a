#!/bin/bash

# Stop Development Environment Script
# This script stops all services started by start-dev-environment.sh

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_PORT=3000
AUTH0_PORT=3002
BACKEND_PORT=8000
POSTGRES_PORT=5434

# PID files
AUTH0_PID_FILE="/tmp/auth0-mock.pid"
FRONTEND_PID_FILE="/tmp/frontend.pid"
BACKEND_PID_FILE="/tmp/backend.pid"

echo -e "${BLUE}🛑 Stopping Development Environment${NC}"
echo "===================================="

# Function to kill process on port
kill_port() {
    local port=$1
    local service_name=$2
    
    echo -e "${YELLOW}🔄 Stopping $service_name on port $port...${NC}"
    
    # Find and kill processes using the port
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    if [ -n "$pids" ]; then
        echo "$pids" | xargs kill -9 2>/dev/null || true
        echo -e "${GREEN}✅ Stopped $service_name${NC}"
    else
        echo -e "${YELLOW}⚠️  No process found on port $port${NC}"
    fi
}

# Function to kill process by PID file
kill_pid_file() {
    local pid_file=$1
    local service_name=$2
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file" 2>/dev/null || echo "")
        if [[ -n "$pid" ]]; then
            echo -e "${YELLOW}🔄 Stopping $service_name (PID: $pid)...${NC}"
            kill "$pid" 2>/dev/null || true
            echo -e "${GREEN}✅ Stopped $service_name${NC}"
        fi
        rm -f "$pid_file"
    fi
}

# Stop services by port
echo -e "\n${BLUE}📍 Stopping services by port...${NC}"
kill_port $FRONTEND_PORT "Frontend"
kill_port $AUTH0_PORT "Auth0 Mock"
kill_port $BACKEND_PORT "Backend API"

# Stop services by PID files
echo -e "\n${BLUE}📄 Stopping services by PID files...${NC}"
kill_pid_file "$AUTH0_PID_FILE" "Auth0 Mock"
kill_pid_file "$FRONTEND_PID_FILE" "Frontend"
kill_pid_file "$BACKEND_PID_FILE" "Backend API"

# Stop Docker services
echo -e "\n${BLUE}🐳 Stopping Docker services...${NC}"

# Check if we're in the correct directory structure
if [ -d "services/api" ]; then
    cd services/api
    echo -e "${YELLOW}🔄 Stopping Docker Compose services...${NC}"
    docker compose -f docker-compose.dev.yml down 2>/dev/null || true
    echo -e "${GREEN}✅ Docker services stopped${NC}"
    cd - > /dev/null
elif [ -f "docker-compose.dev.yml" ]; then
    echo -e "${YELLOW}🔄 Stopping Docker Compose services...${NC}"
    docker compose -f docker-compose.dev.yml down 2>/dev/null || true
    echo -e "${GREEN}✅ Docker services stopped${NC}"
else
    echo -e "${YELLOW}⚠️  No Docker Compose file found${NC}"
fi

# Clean up any remaining processes
echo -e "\n${BLUE}🧹 Final cleanup...${NC}"

# Kill any remaining node processes that might be related
pkill -f "auth0-mock-server" 2>/dev/null || true
pkill -f "next-server" 2>/dev/null || true
pkill -f "uvicorn" 2>/dev/null || true

# Remove temporary log files
rm -f /tmp/auth0-mock.log
rm -f /tmp/auth0-mock-dev.log
rm -f /tmp/frontend.log
rm -f /tmp/frontend-dev.log
rm -f /tmp/backend.log

echo -e "\n${GREEN}🎉 Development Environment Stopped!${NC}"
echo "====================================="
echo -e "${BLUE}Stopped Services:${NC}"
echo -e "  🔐 Auth0 Mock Server"
echo -e "  🌐 Frontend (Next.js)"
echo -e "  🔗 Backend API (if running)"
echo -e "  🐘 PostgreSQL (Docker)"
echo ""
echo -e "${BLUE}Cleaned Up:${NC}"
echo -e "  📄 PID files removed"
echo -e "  📝 Log files removed"
echo -e "  🐳 Docker containers stopped"
echo ""
echo -e "${GREEN}💡 To start again, run:${NC}"
echo -e "  ${YELLOW}npm run dev:auth0${NC}     # Auth0 + Frontend only"
echo -e "  ${YELLOW}npm run dev:full${NC}      # Full development environment"
echo -e "  ${YELLOW}./scripts/start-dev-environment.sh${NC}  # Custom options"
