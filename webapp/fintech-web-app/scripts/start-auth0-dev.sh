#!/bin/bash

# Auth0 Development Environment Starter Script
# This script starts Auth0 mock server and frontend for development with authentication

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_PORT=3000
AUTH0_PORT=3002

# PID files for cleanup
AUTH0_PID_FILE="/tmp/auth0-mock-dev.pid"
FRONTEND_PID_FILE="/tmp/frontend-dev.pid"

# Function to cleanup processes
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up processes...${NC}"
    
    # Kill processes on specific ports
    for port in $AUTH0_PORT $FRONTEND_PORT; do
        echo -e "${YELLOW}🔄 Killing process on port $port...${NC}"
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
    done
    
    # Kill background processes if they exist
    if [[ -f "$AUTH0_PID_FILE" ]]; then
        local auth0_pid=$(cat "$AUTH0_PID_FILE" 2>/dev/null || echo "")
        if [[ -n "$auth0_pid" ]]; then
            kill "$auth0_pid" 2>/dev/null || true
        fi
        rm -f "$AUTH0_PID_FILE"
    fi
    
    if [[ -f "$FRONTEND_PID_FILE" ]]; then
        local frontend_pid=$(cat "$FRONTEND_PID_FILE" 2>/dev/null || echo "")
        if [[ -n "$frontend_pid" ]]; then
            kill "$frontend_pid" 2>/dev/null || true
        fi
        rm -f "$FRONTEND_PID_FILE"
    fi
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Set trap to cleanup on script exit
trap cleanup EXIT

# Function to wait for service
wait_for_service() {
    local service_name="$1"
    local url="$2"
    local timeout="$3"
    
    echo -e "${BLUE}⏳ Waiting for $service_name to be ready...${NC}"
    
    local counter=0
    while [ $counter -lt $timeout ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready!${NC}"
            return 0
        fi
        
        if [ $((counter % 5)) -eq 0 ] && [ $counter -gt 0 ]; then
            echo -e "${YELLOW}⏳ Still waiting for $service_name... (${counter}s)${NC}"
        fi
        
        sleep 1
        counter=$((counter + 1))
    done
    
    echo -e "${RED}❌ $service_name failed to start within ${timeout}s${NC}"
    return 1
}

# Function to check if service is already running
check_service() {
    local service_name="$1"
    local url="$2"
    
    if curl -f -s "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service_name is already running${NC}"
        return 0
    fi
    return 1
}

# Main execution
main() {
    echo -e "${BLUE}🚀 Starting Auth0 Development Environment${NC}"
    echo "=========================================="
    
    # Initial cleanup
    echo -e "\n${YELLOW}🧹 Initial cleanup...${NC}"
    cleanup
    
    # Step 1: Start Auth0 Mock Server
    echo -e "\n${BLUE}🔐 Step 1: Starting Auth0 Mock Server...${NC}"
    
    if check_service "Auth0 Mock" "http://localhost:$AUTH0_PORT/.well-known/openid_configuration"; then
        echo -e "${YELLOW}Auth0 Mock is already running${NC}"
    else
        echo -e "${YELLOW}Starting Auth0 Mock Server...${NC}"
        nohup node scripts/auth0-mock-server.js > /tmp/auth0-mock-dev.log 2>&1 &
        echo $! > "$AUTH0_PID_FILE"

        # Wait for Auth0 Mock
        wait_for_service "Auth0 Mock" "http://localhost:$AUTH0_PORT/.well-known/openid_configuration" 30 || {
            echo -e "${RED}❌ Auth0 Mock failed to start${NC}"
            echo "Auth0 Mock logs:"
            cat /tmp/auth0-mock-dev.log
            exit 1
        }
    fi
    
    # Step 2: Start Frontend
    echo -e "\n${BLUE}🌐 Step 2: Starting frontend...${NC}"
    
    if check_service "Frontend" "http://localhost:$FRONTEND_PORT"; then
        echo -e "${YELLOW}Frontend is already running${NC}"
    else
        # Install frontend dependencies if needed
        if [ ! -d "node_modules" ]; then
            echo -e "${YELLOW}📦 Installing frontend dependencies...${NC}"
            npm install
        fi
        
        # Set environment variables for Auth0 development
        export AUTH0_SECRET='a-very-long-secret-key-for-testing-that-is-at-least-32-characters-long'
        export AUTH0_BASE_URL="http://localhost:$FRONTEND_PORT"
        export AUTH0_ISSUER_BASE_URL="http://localhost:$AUTH0_PORT"
        export AUTH0_CLIENT_ID='my-client-id'
        export AUTH0_CLIENT_SECRET='my-client-secret'
        export AUTH0_AUDIENCE='my-api'
        export LOCAL_AUTH0_URL="http://localhost:$AUTH0_PORT"
        export LOCAL_AUTH0_DOMAIN="localhost:$AUTH0_PORT"
        export NEXT_PUBLIC_AUTH0_DOMAIN="localhost:$AUTH0_PORT"
        export NEXT_PUBLIC_AUTH0_CLIENT_ID='my-client-id'
        export NEXT_PUBLIC_AUTH0_AUDIENCE='my-api'
        export NODE_ENV='development'
        
        echo -e "${YELLOW}🔄 Starting frontend server in background...${NC}"
        PORT=$FRONTEND_PORT nohup npm run dev > /tmp/frontend-dev.log 2>&1 &
        echo $! > "$FRONTEND_PID_FILE"
        
        # Wait for frontend
        wait_for_service "Frontend" "http://localhost:$FRONTEND_PORT" 45 || {
            echo -e "${RED}❌ Frontend failed to start${NC}"
            echo "Frontend logs:"
            tail -50 /tmp/frontend-dev.log
            exit 1
        }
    fi
    
    # Display status
    echo -e "\n${GREEN}🎉 Auth0 Development Environment Ready!${NC}"
    echo "========================================"
    echo -e "${BLUE}Services:${NC}"
    echo -e "  🔐 Auth0 Mock:  ${GREEN}http://localhost:$AUTH0_PORT${NC}"
    echo -e "  🌐 Frontend:    ${GREEN}http://localhost:$FRONTEND_PORT${NC}"
    echo ""
    echo -e "${BLUE}Auth0 Endpoints:${NC}"
    echo -e "  📋 OpenID Config: ${GREEN}http://localhost:$AUTH0_PORT/.well-known/openid_configuration${NC}"
    echo -e "  🔑 JWKS:          ${GREEN}http://localhost:$AUTH0_PORT/.well-known/jwks.json${NC}"
    echo -e "  🔐 Login:         ${GREEN}http://localhost:$AUTH0_PORT/authorize${NC}"
    echo ""
    echo -e "${BLUE}Test Credentials:${NC}"
    echo -e "  📧 Email:    ${YELLOW}<EMAIL>${NC}"
    echo -e "  🔑 Password: ${YELLOW}Test1234${NC}"
    echo ""
    echo -e "${BLUE}Additional Test Users:${NC}"
    echo -e "  📧 E2E User: ${YELLOW}<EMAIL>${NC} / ${YELLOW}E2E1234${NC}"
    echo ""
    echo -e "${BLUE}Development Features:${NC}"
    echo -e "  ✅ Real Auth0 OAuth2 flow simulation"
    echo -e "  ✅ JWT token generation and validation"
    echo -e "  ✅ User registration and login"
    echo -e "  ✅ Error handling for invalid credentials"
    echo -e "  ✅ Session management"
    echo ""
    echo -e "${BLUE}Commands:${NC}"
    echo -e "  🧪 Run Auth Tests: ${YELLOW}npm run test:e2e -- tests/e2e/auth-credentials.spec.ts${NC}"
    echo -e "  📝 View Auth Logs: ${YELLOW}tail -f /tmp/auth0-mock-dev.log${NC}"
    echo -e "  📝 View Frontend Logs: ${YELLOW}tail -f /tmp/frontend-dev.log${NC}"
    echo -e "  🛑 Stop Services: ${YELLOW}Ctrl+C${NC}"
    echo ""
    
    if [ "${WAIT:-true}" = "true" ]; then
        echo -e "${YELLOW}🔄 Services are running. Press Ctrl+C to stop all services...${NC}"
        echo -e "${BLUE}💡 Open ${GREEN}http://localhost:$FRONTEND_PORT${BLUE} in your browser to start development${NC}"
        echo ""
        
        # Keep script running and show periodic status
        while true; do
            sleep 30
            echo -e "${BLUE}$(date '+%H:%M:%S')${NC} - Services running: Auth0 Mock (${AUTH0_PORT}), Frontend (${FRONTEND_PORT})"
        done
    fi
}

# Help function
show_help() {
    echo "Auth0 Development Environment Starter"
    echo "===================================="
    echo ""
    echo "This script starts the Auth0 development environment:"
    echo "  1. Auth0 Mock Server (OAuth2 simulation)"
    echo "  2. Frontend (Next.js with Auth0 integration)"
    echo ""
    echo "Usage:"
    echo "  $0                    Start Auth0 dev environment and wait"
    echo "  $0 --no-wait         Start services and exit"
    echo "  $0 --help            Show this help message"
    echo ""
    echo "Features:"
    echo "  • Complete OAuth2 flow simulation"
    echo "  • JWT token generation and validation"
    echo "  • User authentication testing"
    echo "  • Error handling validation"
    echo ""
    echo "Test Credentials:"
    echo "  Email:    <EMAIL>"
    echo "  Password: Test1234"
    echo ""
    echo "Environment variables:"
    echo "  WAIT=false           Start services and exit immediately"
}

# Parse command line arguments
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    --no-wait)
        export WAIT=false
        main
        ;;
    *)
        main "$@"
        ;;
esac
