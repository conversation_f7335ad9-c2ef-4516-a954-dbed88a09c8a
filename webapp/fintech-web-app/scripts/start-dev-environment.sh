#!/bin/bash

# Development Environment Starter Script
# This script starts all services needed for development

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_DIR="../../services/api"
FRONTEND_PORT=3000
BACKEND_PORT=8000
AUTH0_PORT=3002
POSTGRES_PORT=5434

# PID files for cleanup
AUTH0_PID_FILE="/tmp/auth0-mock.pid"
API_PID_FILE="/tmp/api.pid"
FRONTEND_PID_FILE="/tmp/frontend.pid"

# Function to wait for service
wait_for_service() {
    local service_name="$1"
    local url="$2"
    local timeout="$3"
    
    echo -e "${BLUE}⏳ Waiting for $service_name to be ready...${NC}"
    
    local counter=0
    while [ $counter -lt $timeout ]; do
        if curl -f "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready!${NC}"
            return 0
        fi
        
        if [ $((counter % 10)) -eq 0 ] && [ $counter -gt 0 ]; then
            echo -e "${YELLOW}⏳ Still waiting for $service_name... (${counter}s)${NC}"
        fi
        
        sleep 1
        counter=$((counter + 1))
    done
    
    echo -e "${RED}❌ $service_name failed to start within ${timeout}s${NC}"
    return 1
}

# Function to check if service is already running
check_service() {
    local service_name="$1"
    local url="$2"
    
    if curl -f "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service_name is already running${NC}"
        return 0
    fi
    return 1
}

# Main execution
main() {
    echo -e "${BLUE}🚀 Starting Development Environment${NC}"
    echo "===================================="
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
        exit 1
    fi
    
    # Step 1: Start PostgreSQL Database
    echo -e "\n${BLUE}🔧 Step 1: Starting PostgreSQL database...${NC}"
    cd "$BACKEND_DIR"

    # Start only PostgreSQL (not the Auth0 mock from Docker)
    echo -e "${YELLOW}Starting PostgreSQL database...${NC}"
    docker compose -f docker-compose.dev.yml up -d postgres-dev

    # Wait for PostgreSQL
    wait_for_service "PostgreSQL" "http://localhost:$POSTGRES_PORT" 30 || {
        echo -e "${RED}❌ PostgreSQL failed to start${NC}"
        docker compose -f docker-compose.dev.yml logs postgres-dev
        exit 1
    }

    # Step 2: Start Auth0 Mock Server (Node.js)
    echo -e "\n${BLUE}🔐 Step 2: Starting Auth0 Mock Server...${NC}"
    cd - # Go back to webapp directory

    if check_service "Auth0 Mock" "http://localhost:$AUTH0_PORT/health"; then
        echo -e "${YELLOW}Auth0 Mock is already running${NC}"
    else
        echo -e "${YELLOW}Starting Auth0 Mock Server...${NC}"
        nohup node scripts/auth0-mock-server.js > /tmp/auth0-mock.log 2>&1 &
        echo $! > "$AUTH0_PID_FILE"

        # Wait for Auth0 Mock
        wait_for_service "Auth0 Mock" "http://localhost:$AUTH0_PORT/health" 30 || {
            echo -e "${RED}❌ Auth0 Mock failed to start${NC}"
            echo "Auth0 Mock logs:"
            cat /tmp/auth0-mock.log
            exit 1
        }
    fi
    
    # Step 3: Start Backend API (optional)
    if [ "${START_API:-true}" = "true" ]; then
        echo -e "\n${BLUE}🔗 Step 3: Starting backend API...${NC}"
        cd "$BACKEND_DIR"
        
        if check_service "Backend API" "http://localhost:$BACKEND_PORT/health/"; then
            echo -e "${YELLOW}Backend API is already running${NC}"
        else
            echo -e "${YELLOW}Installing backend dependencies...${NC}"
            uv sync
            
            # Set environment variables
            export DB_HOST=localhost
            export DB_PORT=$POSTGRES_PORT
            export DB_NAME=fintech_dev
            export DB_USER=dev_user
            export DB_PASSWORD=dev_password
            export AUTH0_DOMAIN=localhost:$AUTH0_PORT
            export AUTH0_API_AUDIENCE=my-api
            export AUTH0_ALGORITHMS=HS256
            export AUTH0_ISSUER=http://localhost:$AUTH0_PORT
            
            # Seed the database
            echo -e "${YELLOW}🌱 Seeding development database...${NC}"
            uv run python src/fintech/api/seed_database.py --reset
            
            echo -e "${YELLOW}🔄 Starting API server in background...${NC}"
            nohup uv run uvicorn fintech.api.app:app --host 0.0.0.0 --port $BACKEND_PORT > /tmp/api.log 2>&1 &
            echo $! > "$API_PID_FILE"
            
            # Wait for API
            wait_for_service "Backend API" "http://localhost:$BACKEND_PORT/health/" 30 || {
                echo -e "${RED}❌ Backend API failed to start${NC}"
                echo "API logs:"
                cat /tmp/api.log
                exit 1
            }
        fi
    fi
    
    # Step 4: Start Frontend (optional)
    if [ "${START_FRONTEND:-true}" = "true" ]; then
        echo -e "\n${BLUE}🌐 Step 4: Starting frontend...${NC}"
        cd - # Go back to webapp directory
        
        if check_service "Frontend" "http://localhost:$FRONTEND_PORT"; then
            echo -e "${YELLOW}Frontend is already running${NC}"
        else
            # Install frontend dependencies if needed
            if [ ! -d "node_modules" ]; then
                echo -e "${YELLOW}📦 Installing frontend dependencies...${NC}"
                npm install
            fi
            
            echo -e "${YELLOW}🔄 Starting frontend server in background...${NC}"
            PORT=$FRONTEND_PORT nohup npm run dev > /tmp/frontend.log 2>&1 &
            echo $! > "$FRONTEND_PID_FILE"
            
            # Wait for frontend
            wait_for_service "Frontend" "http://localhost:$FRONTEND_PORT" 30 || {
                echo -e "${RED}❌ Frontend failed to start${NC}"
                echo "Frontend logs:"
                cat /tmp/frontend.log
                exit 1
            }
        fi
    fi
    
    # Display status
    echo -e "\n${GREEN}🎉 Development Environment Ready!${NC}"
    echo "=================================="
    echo -e "${BLUE}Services:${NC}"
    echo -e "  🔐 Auth0 Mock:  ${GREEN}http://localhost:$AUTH0_PORT${NC}"
    echo -e "  🐘 PostgreSQL:  ${GREEN}localhost:$POSTGRES_PORT${NC}"
    
    if [ "${START_API:-true}" = "true" ]; then
        echo -e "  🔗 Backend API:  ${GREEN}http://localhost:$BACKEND_PORT${NC}"
        echo -e "  📚 API Docs:     ${GREEN}http://localhost:$BACKEND_PORT/docs${NC}"
    fi
    
    if [ "${START_FRONTEND:-true}" = "true" ]; then
        echo -e "  🌐 Frontend:     ${GREEN}http://localhost:$FRONTEND_PORT${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}Test User:${NC}"
    echo -e "  📧 Email:    ${YELLOW}<EMAIL>${NC}"
    echo -e "  🔑 Password: ${YELLOW}Test1234${NC}"
    echo ""
    echo -e "${BLUE}Commands:${NC}"
    echo -e "  📊 Status:   ${YELLOW}just status${NC} (from services/api)"
    echo -e "  📝 Logs:     ${YELLOW}just logs${NC} (from services/api)"
    echo -e "  🛑 Stop:     ${YELLOW}just dev-stop${NC} (from services/api)"
    echo ""
    
    if [ "${WAIT:-false}" = "true" ]; then
        echo -e "${YELLOW}Press Ctrl+C to stop all services...${NC}"
        trap 'echo -e "\n${YELLOW}Stopping services...${NC}"; cd "$BACKEND_DIR" && docker compose -f docker-compose.dev.yml down; exit 0' INT
        while true; do sleep 1; done
    fi
}

# Help function
show_help() {
    echo "Development Environment Starter"
    echo "==============================="
    echo ""
    echo "This script starts the development environment:"
    echo "  1. PostgreSQL + Auth0 Mock (Docker)"
    echo "  2. Backend API (FastAPI) [optional]"
    echo "  3. Frontend (Next.js) [optional]"
    echo ""
    echo "Usage:"
    echo "  $0                           Start all services"
    echo "  $0 --services-only           Start only Docker services"
    echo "  $0 --api-only               Start Docker services + API"
    echo "  $0 --wait                   Start all and wait (Ctrl+C to stop)"
    echo "  $0 --help                   Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  START_API=false             Skip starting the API"
    echo "  START_FRONTEND=false        Skip starting the frontend"
    echo "  WAIT=true                   Wait and watch (Ctrl+C to stop)"
}

# Parse command line arguments
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    --services-only)
        export START_API=false
        export START_FRONTEND=false
        main
        ;;
    --api-only)
        export START_FRONTEND=false
        main
        ;;
    --wait)
        export WAIT=true
        main
        ;;
    *)
        main "$@"
        ;;
esac
