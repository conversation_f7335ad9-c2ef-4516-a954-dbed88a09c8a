#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AUTH0_PORT=3002
FRONTEND_PORT=3000

echo -e "${BLUE}🚀 Starting Authentication Credentials E2E Test Environment${NC}"
echo -e "${BLUE}=========================================================${NC}"

# Function to cleanup processes
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up processes...${NC}"
    
    # Kill processes on specific ports
    for port in $AUTH0_PORT $FRONTEND_PORT; do
        echo -e "${YELLOW}🔄 Killing process on port $port...${NC}"
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
    done
    
    # Kill background processes if they exist
    if [[ -n $AUTH0_PID ]]; then
        kill $AUTH0_PID 2>/dev/null || true
    fi
    if [[ -n $FRONTEND_PID ]]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Set trap to cleanup on script exit
trap cleanup EXIT

# Initial cleanup
echo -e "\n${YELLOW}🧹 Initial cleanup...${NC}"
cleanup

# Start Auth0 Mock Server
echo -e "\n${YELLOW}🔐 Starting Auth0 Mock Server...${NC}"
node scripts/auth0-mock-server.js &
AUTH0_PID=$!

# Wait for Auth0 Mock to be ready
echo -e "${YELLOW}⏳ Waiting for Auth0 Mock to be ready...${NC}"
for i in {1..30}; do
    if curl -f -s "http://localhost:$AUTH0_PORT/.well-known/openid_configuration" > /dev/null; then
        echo -e "${GREEN}✅ Auth0 Mock is ready!${NC}"
        break
    fi
    echo -e "${YELLOW}   Attempt $i/30 - Auth0 Mock not ready yet...${NC}"
    sleep 2
done

if ! curl -f -s "http://localhost:$AUTH0_PORT/.well-known/openid_configuration" > /dev/null; then
    echo -e "${RED}❌ Auth0 Mock failed to start${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Auth0 Mock is running on port $AUTH0_PORT${NC}"

# Start Frontend
echo -e "\n${YELLOW}🔄 Starting frontend server...${NC}"
npm run dev &
FRONTEND_PID=$!

# Wait for Frontend to be ready
echo -e "${YELLOW}⏳ Waiting for Frontend to be ready...${NC}"
for i in {1..30}; do
    if curl -f -s "http://localhost:$FRONTEND_PORT" > /dev/null; then
        echo -e "${GREEN}✅ Frontend is ready!${NC}"
        break
    fi
    echo -e "${YELLOW}   Attempt $i/30 - Frontend not ready yet...${NC}"
    sleep 2
done

if ! curl -f -s "http://localhost:$FRONTEND_PORT" > /dev/null; then
    echo -e "${RED}❌ Frontend failed to start${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Frontend is running on port $FRONTEND_PORT${NC}"

# Verify all services
echo -e "\n${BLUE}🔍 Verifying all services...${NC}"

# Test Auth0 Mock
echo -e "${YELLOW}🔄 Testing Auth0 Mock...${NC}"
if curl -f -s "http://localhost:$AUTH0_PORT/.well-known/openid_configuration" > /dev/null; then
    echo -e "${GREEN}✅ Auth0 Mock is responding${NC}"
else
    echo -e "${RED}❌ Auth0 Mock is not responding${NC}"
    exit 1
fi

# Test Frontend
echo -e "${YELLOW}🔄 Testing Frontend...${NC}"
if curl -f -s "http://localhost:$FRONTEND_PORT" > /dev/null; then
    echo -e "${GREEN}✅ Frontend is responding${NC}"
else
    echo -e "${RED}❌ Frontend is not responding${NC}"
    exit 1
fi

# Set environment variables for E2E tests
export AUTH0_SECRET='a-very-long-secret-key-for-testing-that-is-at-least-32-characters-long'
export AUTH0_BASE_URL="http://localhost:$FRONTEND_PORT"
export AUTH0_ISSUER_BASE_URL="http://localhost:$AUTH0_PORT"
export AUTH0_CLIENT_ID='my-client-id'
export AUTH0_CLIENT_SECRET='my-client-secret'
export AUTH0_AUDIENCE='my-api'
export LOCAL_AUTH0_URL="http://localhost:$AUTH0_PORT"
export LOCAL_AUTH0_DOMAIN="localhost:$AUTH0_PORT"
export NEXT_PUBLIC_AUTH0_DOMAIN="localhost:$AUTH0_PORT"
export NEXT_PUBLIC_AUTH0_CLIENT_ID='my-client-id'
export NEXT_PUBLIC_AUTH0_AUDIENCE='my-api'
export NODE_ENV='development'

# Run the Authentication Credentials E2E tests
echo -e "\n${GREEN}🎭 All services are ready! Running Authentication Credentials E2E tests...${NC}"
echo -e "${BLUE}================================================================${NC}"

# Run the specific authentication credentials tests
npm run test:e2e -- tests/e2e/auth-credentials.spec.ts --reporter=list

# Check test results
if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All Authentication Credentials E2E tests passed!${NC}"
    echo -e "${GREEN}✅ Correct credentials authentication is working${NC}"
    echo -e "${GREEN}✅ Wrong credentials show proper error messages${NC}"
    echo -e "${GREEN}✅ Non-existent user shows proper error messages${NC}"
    echo -e "${GREEN}✅ Empty credentials are handled correctly${NC}"
else
    echo -e "\n${RED}❌ Some Authentication Credentials E2E tests failed${NC}"
    echo -e "${RED}Check the test output above for details${NC}"
    exit 1
fi

echo -e "\n${BLUE}🏁 Authentication Credentials E2E Test Run Complete!${NC}"
echo -e "${BLUE}===================================================${NC}"
echo -e "${GREEN}✅ Environment: Auth0 Mock + Frontend${NC}"
echo -e "${GREEN}✅ Authentication: Real Auth0 flow with JWT tokens${NC}"
echo -e "${GREEN}✅ Test Coverage: Success + Failure scenarios${NC}"

echo -e "\n${YELLOW}💡 Test Credentials Used:${NC}"
echo -e "${YELLOW}   ✅ Correct: <EMAIL> / Test1234${NC}"
echo -e "${YELLOW}   ❌ Wrong: <EMAIL> / WrongPassword123${NC}"
echo -e "${YELLOW}   ❌ Non-existent: <EMAIL> / SomePassword123${NC}"
echo -e "${YELLOW}   ❌ Empty: (no credentials)${NC}"

echo -e "\n${YELLOW}🔧 Services used:${NC}"
echo -e "${YELLOW}   Frontend: http://localhost:$FRONTEND_PORT${NC}"
echo -e "${YELLOW}   Auth0 Mock: http://localhost:$AUTH0_PORT${NC}"

echo -e "\n${GREEN}✅ All tests completed successfully!${NC}"
