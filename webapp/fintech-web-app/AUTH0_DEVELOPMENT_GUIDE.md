# Auth0 Development Guide

This guide covers all the Auth0 development and testing options available in the fintech web application.

## 🚀 Quick Start Options

### Option 1: Auth0-Only Development (Recommended for Auth Testing)

Start just the Auth0 mock server and frontend for lightweight authentication development:

```bash
# Using npm script (recommended)
npm run dev:auth0

# Or using script directly
./scripts/start-auth0-dev.sh
```

**What you get:**
- ✅ Auth0 Mock Server (localhost:3002)
- ✅ Frontend with Auth0 integration (localhost:3000)
- ✅ Complete OAuth2 flow simulation
- ✅ JWT token generation and validation
- ✅ User registration and login testing
- ✅ Error handling validation

### Option 2: Full Development Environment

Start all services including database and API:

```bash
# Using npm script
npm run dev:full

# Or using script directly
./scripts/start-dev-environment.sh
```

**What you get:**
- ✅ PostgreSQL Database (localhost:5434)
- ✅ Auth0 Mock Server (localhost:3002)
- ✅ Backend API (localhost:8000)
- ✅ Frontend (localhost:3000)

### Option 3: Auth0 + Frontend Only (via dev script)

Use the enhanced development script with Auth0-only option:

```bash
# Using npm script
npm run dev:auth0-only

# Or using script directly
./scripts/start-dev-environment.sh --auth0-only
```

## 🧪 Testing Authentication

### Run Authentication Credential Tests

Test both successful and failed authentication scenarios:

```bash
# Using npm script (recommended)
npm run test:e2e:auth-credentials

# Or using script directly
./scripts/run-auth-credentials-e2e.sh
```

**Test Scenarios Covered:**
- ✅ **Correct Credentials**: `<EMAIL>` / `Test1234`
- ❌ **Wrong Password**: `<EMAIL>` / `WrongPassword123`
- ❌ **Non-existent User**: `<EMAIL>` / `SomePassword123`
- ❌ **Empty Credentials**: No input provided

### Run Auth0 Real Flow Tests

Test complete user registration and authentication flows:

```bash
npm run test:e2e:auth0
```

## 🔐 Test Credentials

### Pre-configured Users

| Email | Password | Description |
|-------|----------|-------------|
| `<EMAIL>` | `Test1234` | Main test user |
| `<EMAIL>` | `E2E1234` | E2E test user |

### Auth0 Configuration

| Setting | Value |
|---------|-------|
| Domain | `localhost:3002` |
| Client ID | `my-client-id` |
| Client Secret | `my-client-secret` |
| Audience | `my-api` |
| Issuer | `http://localhost:3002` |

## 🌐 Service URLs

| Service | URL | Description |
|---------|-----|-------------|
| Frontend | http://localhost:3000 | Main application |
| Auth0 Mock | http://localhost:3002 | OAuth2 server |
| OpenID Config | http://localhost:3002/.well-known/openid_configuration | Auth0 configuration |
| JWKS | http://localhost:3002/.well-known/jwks.json | JWT keys |
| Backend API | http://localhost:8000 | API server (if running) |
| API Docs | http://localhost:8000/docs | API documentation (if running) |
| PostgreSQL | localhost:5434 | Database (if running) |

## 🛠️ Development Features

### Auth0 Mock Server Features

- **Complete OAuth2 Flow**: Authorization code flow with PKCE
- **JWT Token Generation**: Real JWT tokens with proper claims
- **User Management**: Registration, login, user info endpoints
- **Error Handling**: Proper error responses for invalid credentials
- **JWKS Endpoint**: Public key discovery for token validation
- **OpenID Configuration**: Standard discovery endpoint

### Frontend Integration

- **Auth0 SDK**: Uses `@auth0/nextjs-auth0` for seamless integration
- **Session Management**: Automatic session handling and refresh
- **Protected Routes**: Route protection based on authentication state
- **User Profile**: Access to user information and profile data
- **Logout Handling**: Proper logout and session cleanup

## 📝 Development Workflow

### 1. Start Development Environment

```bash
# For Auth0 development only
npm run dev:auth0

# For full stack development
npm run dev:full
```

### 2. Open Application

Navigate to http://localhost:3000 in your browser.

### 3. Test Authentication

1. Go through the marketing screens
2. Click "Get Started" to reach login
3. Click "Continue with Auth0"
4. Use test credentials: `<EMAIL>` / `Test1234`

### 4. Run Tests

```bash
# Test authentication scenarios
npm run test:e2e:auth-credentials

# Test complete Auth0 flows
npm run test:e2e:auth0
```

### 5. View Logs

```bash
# Auth0 Mock Server logs
tail -f /tmp/auth0-mock-dev.log

# Frontend logs
tail -f /tmp/frontend-dev.log
```

## 🔧 Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check what's using a port
   lsof -ti:3000
   
   # Kill process using port
   kill $(lsof -ti:3000)
   ```

2. **Services Not Starting**
   ```bash
   # Check logs
   tail -f /tmp/auth0-mock-dev.log
   tail -f /tmp/frontend-dev.log
   ```

3. **Authentication Not Working**
   - Verify Auth0 mock server is running on port 3002
   - Check environment variables are set correctly
   - Ensure frontend is using correct Auth0 configuration

### Environment Variables

The following environment variables are automatically set by the development scripts:

```bash
AUTH0_SECRET='a-very-long-secret-key-for-testing-that-is-at-least-32-characters-long'
AUTH0_BASE_URL='http://localhost:3000'
AUTH0_ISSUER_BASE_URL='http://localhost:3002'
AUTH0_CLIENT_ID='my-client-id'
AUTH0_CLIENT_SECRET='my-client-secret'
AUTH0_AUDIENCE='my-api'
NEXT_PUBLIC_AUTH0_DOMAIN='localhost:3002'
NEXT_PUBLIC_AUTH0_CLIENT_ID='my-client-id'
NEXT_PUBLIC_AUTH0_AUDIENCE='my-api'
```

## 🎯 Best Practices

1. **Use Auth0-Only Mode** for authentication development to avoid unnecessary services
2. **Run Tests Regularly** to ensure authentication flows work correctly
3. **Check Logs** when debugging authentication issues
4. **Use Test Credentials** consistently across development and testing
5. **Clean Up Processes** when switching between different development modes

## 📚 Additional Resources

- [Auth0 Next.js SDK Documentation](https://auth0.com/docs/quickstart/webapp/nextjs)
- [OAuth2 Authorization Code Flow](https://auth0.com/docs/get-started/authentication-and-authorization-flow/authorization-code-flow)
- [JWT Token Validation](https://auth0.com/docs/secure/tokens/json-web-tokens/validate-json-web-tokens)
