import { User, BankCard, Goal, Transaction, SavingSettings } from './types';
import { Coffee, ShoppingBag, Car, Home, Utensils } from 'lucide-react';

// Mock user data
export const mockUser: User = {
  email: '<EMAIL>',
  name: '<PERSON>',
};

// Mock bank cards
export const mockCards: BankCard[] = [
  {
    id: '1',
    name: 'Main Checking',
    type: 'Debit',
    cardNumber: '****************',
    cvv: '123',
    expirationDate: '12/25',
    lastFour: '9012',
  },
  {
    id: '2',
    name: 'Travel Rewards',
    type: 'Credit',
    cardNumber: '****************',
    cvv: '456',
    expirationDate: '08/26',
    lastFour: '2222',
    limit: 5000,
  },
];

// Mock goals
export const mockGoals: Goal[] = [
  {
    id: '1',
    name: 'Emergency Fund',
    target: 10000,
    deadline: new Date('2024-12-31'),
    saved: 3500,
    strategy: 'Fixed Income',
    isActive: true,
  },
  {
    id: '2',
    name: 'Vacation',
    target: 5000,
    deadline: new Date('2024-08-15'),
    saved: 1200,
    strategy: 'Standard',
    isActive: false,
  },
  {
    id: '3',
    name: 'New Car',
    target: 25000,
    deadline: new Date('2025-06-01'),
    saved: 8750,
    strategy: 'Growth',
    isActive: true,
  },
];

// Mock transactions
export const mockTransactions: Transaction[] = [
  {
    id: '1',
    merchant: 'Coffee Shop',
    amount: -4.50,
    savings: 0.50,
    method: 'roundups',
    icon: Coffee,
    time: '10:30 AM',
  },
  {
    id: '2',
    merchant: 'Online Shopping',
    amount: -89.99,
    savings: 0.01,
    method: 'roundups',
    icon: ShoppingBag,
    time: '2:15 PM',
  },
  {
    id: '3',
    merchant: 'Gas Station',
    amount: -45.20,
    savings: 0.80,
    method: 'roundups',
    icon: Car,
    time: '8:45 AM',
  },
  {
    id: '4',
    merchant: 'Salary Deposit',
    amount: 3200.00,
    savings: 50.00,
    method: 'percentage',
    icon: Home,
    time: '9:00 AM',
  },
  {
    id: '5',
    merchant: 'Restaurant',
    amount: -67.80,
    savings: 0.20,
    method: 'roundups',
    icon: Utensils,
    time: '7:30 PM',
  },
];

// Mock saving settings
export const mockSavingSettings: SavingSettings = {
  roundups: true,
  percentage: false,
  percentageValue: 5,
  recurring: true,
  recurringAmount: 50,
  recurringFrequency: 'weekly',
};
