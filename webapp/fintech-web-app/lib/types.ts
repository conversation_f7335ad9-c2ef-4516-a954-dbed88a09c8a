export type CardType = 'Debit' | 'Credit';

export type Screen =
  | 'marketing'
  | 'login'
  | 'dashboard'
  | 'add-card'
  | 'contributions'
  | 'investments'
  | 'profile'
  | 'add-goal'
  | 'savings-methods'
  | 'manage-goals'
  | 'bank-accounts'
  | 'retirement-accounts'
  | 'compounding-calculator';

export type InvestmentStrategy = 'Fixed Income' | 'Standard' | 'Growth';
export type SavingMethod = 'roundups' | 'percentage' | 'recurring';

export interface BankCard {
  id: string;
  name: string;
  type: CardType;
  cardNumber: string;
  cvv: string;
  expirationDate: string;
  limit?: number;
  lastFour?: string;
}

export interface PortfolioAllocation {
  equities: number;
  bonds: number;
  cash: number;
}

export interface Goal {
  id: string;
  name: string;
  target: number;
  deadline: Date;
  saved: number;
  strategy: InvestmentStrategy;
  isActive: boolean;
  timeHorizon?: number; // in years
  allocation?: PortfolioAllocation;
  description?: string;
}

export interface Transaction {
  id: string;
  merchant: string;
  amount: number;
  savings: number;
  method: SavingMethod;
  icon: React.ComponentType<{ className?: string }>;
  time: string;
}

export interface SavingSettings {
  roundups: boolean;
  percentage: boolean;
  percentageValue: number;
  recurring: boolean;
  recurringAmount: number;
  recurringFrequency: string;
}

export interface User {
  email: string;
  name: string;
}

export interface CardFormData {
  name: string;
  type: CardType;
  cardNumber: string;
  cvv: string;
  expirationDate: string;
  limit: string;
}

export interface GoalFormData {
  name: string;
  target: string;
  deadline: string;
}

export interface LoginFormData {
  email: string;
  password: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface InvestmentStrategyDetails {
  risk: string;
  return: string;
  description: string;
  projection: string;
  color: string;
}

// New interfaces for enhanced features
export interface BankAccount {
  id: string;
  name: string;
  bankName: string;
  accountType: 'checking' | 'savings';
  lastFour: string;
  balance: number;
  isLinked: boolean;
  linkedDate?: Date;
}

export interface RetirementAccount {
  id: string;
  name: string;
  type: 'swiss_3a' | 'german_riester' | 'us_401k' | 'uk_pension';
  country: string;
  balance: number;
  contributionLimit: number;
  currentYearContribution: number;
}

export interface CompoundingCalculatorInput {
  initialAmount: number;
  monthlyContribution: number;
  interestRate: number;
  years: number;
}

export interface CompoundingResult {
  year: number;
  balance: number;
  totalContributions: number;
  interestEarned: number;
}

export interface PortfolioSummary {
  cashTotal: number;
  investmentTotal: number;
  nestsTotal: number;
  totalBalance: number;
  monthlyGrowth: number;
  ytdGrowth: number;
  sparklineData: number[];
}
