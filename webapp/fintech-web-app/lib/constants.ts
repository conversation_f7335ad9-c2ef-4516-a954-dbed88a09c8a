import { Bird, Coffee, TrendingUp, Settings, Target, Egg, Banknote, PiggyBank, Calculator } from 'lucide-react';

export const marketingScreens = [
  {
    title: 'Save effortlessly as you spend',
    subtitle: 'Your money works harder for you',
    description: 'Transform every purchase into progress toward your dreams',
    icon: Bird,
    gradient: 'from-orange-400 to-amber-500',
  },
  {
    title: 'Every coffee pays into your future',
    subtitle: 'Small changes, big impact',
    description: 'Round up purchases and watch your savings grow automatically',
    icon: Coffee,
    gradient: 'from-amber-400 to-orange-500',
  },
  {
    title: 'Invest your spare change into curated ETFs',
    subtitle: 'Professional investing made simple',
    description: 'Your savings are invested in diversified portfolios by experts',
    icon: TrendingUp,
    gradient: 'from-orange-400 to-red-500',
  },
  {
    title: 'Choose how you save',
    subtitle: 'Round-ups, % of spending, or recurring transfers',
    description: 'Multiple ways to build wealth that fit your lifestyle',
    icon: Settings,
    gradient: 'from-red-400 to-pink-500',
  },
  {
    title: "Set your goal — we'll help you reach it",
    subtitle: 'Dream big, save smart',
    description: "Whether it's a vacation or a house, we'll create a plan",
    icon: Target,
    gradient: 'from-pink-400 to-purple-500',
  },
  {
    title: 'Watch your savings grow',
    subtitle: 'Smart investment strategies',
    description: 'Based on proven strategies that compound your wealth over time',
    icon: Egg,
    gradient: 'from-purple-400 to-indigo-500',
  },
];

export const investmentStrategies = {
  'Fixed Income': {
    risk: 'Low Risk',
    return: '3-4%',
    description: 'Conservative approach with steady, predictable returns',
    projection: '€1,450 by July 2026',
    color: 'text-green-600',
  },
  Standard: {
    risk: 'Balanced Risk',
    return: '5-6%',
    description: 'Moderate growth with balanced risk and reward',
    projection: '€2,130 in 11 months',
    color: 'text-orange-600',
  },
  Growth: {
    risk: 'High Risk',
    return: '7-10%',
    description: 'Aggressive growth for long-term wealth building',
    projection: 'May exceed €2,000 goal',
    color: 'text-red-600',
  },
} as const;

// Portfolio allocation defaults for different strategies
export const portfolioAllocations = {
  'Fixed Income': {
    equities: 20,
    bonds: 70,
    cash: 10,
  },
  Standard: {
    equities: 60,
    bonds: 30,
    cash: 10,
  },
  Growth: {
    equities: 80,
    bonds: 15,
    cash: 5,
  },
} as const;

// Retirement account types by country
export const retirementAccountTypes = {
  switzerland: [
    { value: 'swiss_3a', label: 'Pillar 3a', limit: 7056 },
  ],
  germany: [
    { value: 'german_riester', label: 'Riester Pension', limit: 2100 },
  ],
  usa: [
    { value: 'us_401k', label: '401(k)', limit: 23000 },
  ],
  uk: [
    { value: 'uk_pension', label: 'Personal Pension', limit: 60000 },
  ],
} as const;

// Mock data for development
export const mockBankAccounts = [
  {
    id: '1',
    name: 'Main Checking',
    bankName: 'Chase Bank',
    accountType: 'checking' as const,
    lastFour: '1234',
    balance: 2450.75,
    isLinked: true,
    linkedDate: new Date('2024-01-15'),
  },
  {
    id: '2',
    name: 'Savings Account',
    bankName: 'Wells Fargo',
    accountType: 'savings' as const,
    lastFour: '5678',
    balance: 8920.50,
    isLinked: true,
    linkedDate: new Date('2024-02-20'),
  },
];

export const mockRetirementAccounts = [
  {
    id: '1',
    name: 'Swiss 3a Account',
    type: 'swiss_3a' as const,
    country: 'Switzerland',
    balance: 15420.30,
    contributionLimit: 7056,
    currentYearContribution: 3500,
  },
];
