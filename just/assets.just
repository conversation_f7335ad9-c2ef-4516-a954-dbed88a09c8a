aws := require('aws')
npm := require('npm')

# Configurable variables:
assets-bucket-prefix := 'fintech-api-assets'
assets-exclude       := '.* README.md'

# Deploys a static bundle <src> of assets to <dst> in S3, in the given <env>.
[group: 'Assets']
assets-deploy env src dest:
    #!/usr/bin/env bash
    set -euo pipefail

    BUCKET="{{assets-bucket-prefix}}-{{env}}"
    EXCLUDES=$(echo '{{assets-exclude}}' | perl -pe "s/([^\s]+)/--exclude '\1'/g")

    echo -n "Assuming role for the {{env}} environment... "
    source "{{source_dir()}}/assume-role.sh"
    assume_role "{{env}}"

    eval {{aws}} s3 sync "{{src}}" "s3://$BUCKET/{{dest}}" $EXCLUDES


[group: 'Assets']
upload-web-app env s3-bucket:
    #!/bin/bash
    set -e  # Exit on error

    echo "Clean out folder"
    rm -rf out
    BUCKET_NAME={{s3-bucket}}

    echo "Installing dependencies..."
    {{npm}} install

    echo "Building the project..."
    {{npm}} run build

    echo "Uploading to S3 bucket: $BUCKET_NAME"
    {{aws}} s3 sync out/ s3://$BUCKET_NAME --delete

    echo "Deployment complete!"