aws    := require("aws")
docker := require("docker")

# Configurable variables:
docker-dir  := '.'
docker-file := 'Dockerfile'

# Build the Docker image, tagging it with the given <image> name and <version>.
[group: 'Docker']
docker-build image version *extra-args:
    #!/usr/bin/env bash
    set -euo pipefail

    cd {{docker-dir}}
    {{docker}} build \
        -t {{image}}:{{version}} \
        -f {{docker-file}} \
        {{extra-args}} .

# Push the given Docker <image>'s <version> to a corresponding ECR repository in the current AWS account.
[group: 'Docker']
docker-push-ecr env image version:
    #!/usr/bin/env bash
    set -euo pipefail

    cd {{docker-dir}}
    
    # Just find the proper repo in the current AWS account (shared), by name:
    ECR_REPO_URI=$({{aws}} ecr describe-repositories --repository-name "{{image}}" --query 'repositories[0].repositoryUri' --output text)

    # Ensure the image doesn't exist in the ECR repository yet:
    EXISTING_IMAGE=$({{aws}} ecr describe-images --repository-name "{{image}}" --image-ids=imageTag={{version}} || true)
    if [ -n "$EXISTING_IMAGE" ]; then
        echo "Error: {{image}}:{{version}} already exists in the ECR repository!";
        exit 1;
    fi

    # Assume the proper per-environment role with push permissions:
    echo -n "Assuming role for the {{env}} environment... "
    source "{{source_dir()}}/assume-role.sh"
    assume_role "{{env}}"
    
    # Authenticate Docker to the ECR registry:
    ECR_REGISTRY_URI=$(echo "$ECR_REPO_URI" | cut -d'/' -f1)
    {{aws}} ecr get-login-password | docker login --username AWS --password-stdin "$ECR_REGISTRY_URI"

    # Tag the Docker image with the ECR repository URI:
    {{docker}} tag {{image}}:{{version}} "$ECR_REPO_URI:{{version}}"
    {{docker}} push "$ECR_REPO_URI:{{version}}"
