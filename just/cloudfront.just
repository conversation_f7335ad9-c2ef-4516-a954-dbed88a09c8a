aws := require('aws')

# Set a CF <secret> in the given key-value <store> and the given <env> to <value>.
[group: 'CloudFront']
cf-set-secret env store-arn secret-name value:
    #!/usr/bin/env bash
    set -euo pipefail

    source "{{source_dir()}}/assume-role.sh"
    assume_role "{{env}}"

    KVS_ETAG=$(
        {{aws}} cloudfront-keyvaluestore describe-key-value-store \
        --kvs-arn "{{store-arn}}" \
        --query "@.ETag" \
        --output text \
    )

    {{aws}} cloudfront-keyvaluestore put-key \
        --kvs-arn "{{store-arn}}" \
        --if-match "$KVS_ETAG" \
        --key "{{secret-name}}" \
        --value "{{value}}"

# Delete a CF <secret> from the given key-value <store> in the given <env>.
[group: 'CloudFront']
cf-delete-secret env store-arn secret-name:
    #!/usr/bin/env bash
    set -euo pipefail

    source "{{source_dir()}}/assume-role.sh"
    assume_role "{{env}}"

    KVS_ETAG=$(
        {{aws}} cloudfront-keyvaluestore describe-key-value-store \
        --kvs-arn "{{store-arn}}" \
        --query "@.ETag" \
        --output text \
    )

    {{aws}} cloudfront-keyvaluestore delete-key \
        --kvs-arn "{{store-arn}}" \
        --if-match "$KVS_ETAG" \
        --key "{{secret-name}}"
