aws := require('aws')
docker := require('docker')

# Update the given Lambda function to use the given Docker image.
[group: 'Lambda']
lambda-deploy-docker env func-name image version:
    #!/usr/bin/env bash
    set -euo pipefail

    FULL_IMAGE_REF=$({{docker}} images --filter reference='*ecr*/{{image}}:{{version}}' --format '{{{{.Repository}}:{{{{.Tag}}')
    FULL_FUNC_NAME="{{func-name}}-{{env}}"

    echo -n "Assuming role for the {{env}} environment... "
    source "{{source_dir()}}/assume-role.sh"
    assume_role "{{env}}"

    echo "Deploying $FULL_IMAGE_REF to Lambda function $FULL_FUNC_NAME..."

    {{aws}} lambda update-function-code \
        --function-name "$FULL_FUNC_NAME" \
        --image-uri "$FULL_IMAGE_REF"
    {{aws}} lambda wait function-updated-v2 \
        --function-name "$FULL_FUNC_NAME"

    echo "Lambda function $FULL_FUNC_NAME updated successfully."
