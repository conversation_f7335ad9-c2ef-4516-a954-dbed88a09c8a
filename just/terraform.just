terraform := require('terraform')

# Configurable variables:
tf-dir := '.'

# Initialize Terraform for the given <env>.
[group: 'Terraform']
tf-setup env:
    #!/usr/bin/env bash
    set -euo pipefail
    
    cd {{tf-dir}}
    {{terraform}} init -upgrade
    {{terraform}} workspace select {{env}} || {{terraform}} workspace new {{env}}

# Validate the Terraform configuration.
[group: 'Terraform']
tf-validate:
    #!/usr/bin/env bash
    set -euo pipefail
    
    cd {{tf-dir}}
    {{terraform}} validate

# Show the Terraform plan for the given <env>.
[group: 'Terraform']
tf-plan env: (tf-setup env)
    #!/usr/bin/env bash
    set -euo pipefail

    cd {{tf-dir}}
    {{terraform}} workspace select {{env}}
    {{terraform}} plan

# Apply the Terraform plan for the given <env>.
[group: 'Terraform']
tf-deploy env: (tf-setup env)
    #!/usr/bin/env bash
    set -euo pipefail

    cd {{tf-dir}}
    {{terraform}} workspace select {{env}}
    {{terraform}} apply
