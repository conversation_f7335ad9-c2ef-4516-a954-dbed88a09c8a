# Generate OpenAPI spec from AWS API Gateway <api-name> in <env> and stores them in <out-file>.
[group: 'OpenAPI']
openapi-import-apigw env api-name out-file:
    #!/usr/bin/env bash
    set -euo pipefail
    echo -n "Assuming role for the {{env}} environment... "
    source "{{source_dir()}}/assume-role.sh"
    assume_role "{{env}}"

    DOCS_DIR=$(dirname "{{out-file}}")
    OUT_FILE=$(basename "{{out-file}}")

    cd "$DOCS_DIR"

    API_ID=$({{aws}} apigateway get-rest-apis --query "items[?name=='{{api-name}}'].id" --output text)
    {{aws}} apigateway get-export \
      --rest-api-id $API_ID \
      --stage-name {{env}} \
      --export-type oas30 \
      "$OUT_FILE"

# Merge the given OpenAPI spec <base-file> with definitions from the provided <patch-files>.
[group: 'OpenAPI']
openapi-merge base-file +patch-files:
    #!/usr/bin/env bash
    set -euo pipefail

    JQ_SLURP=""
    JQ_EXPR="."
    ITER=0
    for PATCH_FILE in {{patch-files}}; do
        JQ_SLURP+=" --slurpfile patch$ITER \"$PATCH_FILE\""
        JQ_EXPR+=" * \$patch$ITER[0]"
        ITER=$((ITER + 1))
    done

    JQ_CMD="jq -r $JQ_SLURP '$JQ_EXPR' {{base-file}}"
    eval "$JQ_CMD" > "{{base-file}}.tmp" && mv "{{base-file}}.tmp" "{{base-file}}"

# Modify the given OpenAPI spec <base-file> using the given <jq-expression> and "key=value" <substitutions>.
[group: 'OpenAPI']
openapi-modify base-file jq-expression *substitutions:
    #!/usr/bin/env bash
    set -euo pipefail

    JQ_VARS=""
    for SUB in {{substitutions}}; do
        KEY=$(echo "$SUB" | cut -d'=' -f1)
        VAL=$(echo "$SUB" | cut -d'=' -f2-)
        JQ_VARS+=" --arg $KEY \"$VAL\""
    done

    JQ_EXPR='{{jq-expression}}'
    JQ_CMD="jq -r $JQ_VARS '$JQ_EXPR' {{base-file}}"
    eval "$JQ_CMD" > "{{base-file}}.tmp" && mv "{{base-file}}.tmp" "{{base-file}}"
