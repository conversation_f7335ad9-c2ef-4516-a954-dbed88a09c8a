# Note: Associative arrays with declare -A are not supported before Bash 4.0 (vide macOS).
ROLE_dev="arn:aws:iam::407276168849:role/tf-infra-dev-deploy-role"
ROLE_prod="arn:aws:iam::304848207041:role/tf-infra-prod-deploy-role"

assume_role() {
  local ROLE_VAR="ROLE_$1"
  local ROLE_ARN=$(printf "%s" "${!ROLE_VAR}")
  echo "$ROLE_ARN"

  ASSUME_ROLE_OUTPUT=$(aws sts assume-role \
    --role-arn "$ROLE_ARN" \
    --role-session-name "LambdaDeploySession")

  export AWS_ACCESS_KEY_ID=$(echo "$ASSUME_ROLE_OUTPUT" | grep -o '"AccessKeyId": "[^"]*' | sed 's/"AccessKeyId": "//')
  export AWS_SECRET_ACCESS_KEY=$(echo "$ASSUME_ROLE_OUTPUT" | grep -o '"SecretAccessKey": "[^"]*' | sed 's/"SecretAccessKey": "//')
  export AWS_SESSION_TOKEN=$(echo "$ASSUME_ROLE_OUTPUT" | grep -o '"SessionToken": "[^"]*' | sed 's/"SessionToken": "//')
  export AWS_SECURITY_TOKEN=$AWS_SESSION_TOKEN
  export AWS_PAGER=""  # Prevent AWS CLI from opening output in a pager; this ensures commands run non-interactively
}
