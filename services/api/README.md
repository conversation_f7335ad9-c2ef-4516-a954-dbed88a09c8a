# FinTech API Service

A FastAPI-based REST API service for the FinTech application with PostgreSQL database integration and automatic seeding.

## 🚀 Quick Start

### Easy Start (Recommended)

```bash
# Navigate to API service directory
cd services/api

# Install dependencies
uv sync

# Start everything (database + API) with one command
./start_server.sh
```

This script will:
- ✅ Automatically start PostgreSQL database if not running
- ✅ Set correct database environment variables
- ✅ Seed database with sample data
- ✅ Start API server with uvicorn and auto-reload on http://localhost:8000

### Manual Start

### Prerequisites

- Python 3.12+
- Docker and Docker Compose
- UV package manager

### 1. Start PostgreSQL Database

```bash
# Navigate to models directory and start PostgreSQL container
cd libs/models
docker compose -f docker-compose.test.yml up -d

# Verify database is running
docker ps | grep postgres
```

### 2. Install Dependencies

```bash
# Navigate to API service directory
cd services/api

# Install dependencies with UV
uv sync
```

### 3. Seed Database (Optional - Manual Seeding)

```bash
# Seed database with sample data (optional, server does this automatically)
DB_HOST=localhost DB_PORT=5433 DB_NAME=fintech_test DB_USER=test_user DB_PASSWORD=test_password \
uv run python src/fintech/api/seed_database.py

# Reset and re-seed database if needed
DB_HOST=localhost DB_PORT=5433 DB_NAME=fintech_test DB_USER=test_user DB_PASSWORD=test_password \
uv run python src/fintech/api/seed_database.py --reset
```

### 4. Start API Server

```bash
# Start database first
cd ../../libs/models
docker compose -f docker-compose.test.yml up -d
cd ../../services/api

# Set environment variables and start with uvicorn
DB_HOST=localhost DB_PORT=5433 DB_NAME=fintech_test DB_USER=test_user DB_PASSWORD=test_password \
uv run uvicorn fintech.api.app:app --reload --host 0.0.0.0 --port 8000
```

### Development Features

✅ **Auto-reload**: Server automatically restarts when you change code
✅ **Hot reloading**: Changes are reflected immediately without manual restart
✅ **Development server**: Uvicorn with reload functionality for fast development
✅ **Database seeding**: Automatic database setup with sample data

The server will:
- ✅ Connect to PostgreSQL database using existing models library
- ✅ Automatically seed database with sample data (if not already seeded)
- ✅ Use proper database models, repositories, and schemas
- ✅ Start API server on http://localhost:8000

## 📊 Database Schema

The API uses a simple, focused database schema:

### Tables Created
- **users** - User profiles and account information
- **cards** - Payment cards linked to users  
- **transactions** - Financial transactions from cards

### Sample Data Seeded
- **1 User**: John Doe (<EMAIL>)
- **2 Cards**: Main Debit Card, Credit Card
- **5 Transactions**: Coffee shops, groceries, online purchases

## 🔗 API Endpoints

Once the server is running, you can access:

### Core Endpoints
- **Health Check**: `GET /health`
- **API Info**: `GET /`
- **Seed Status**: `GET /seed-status`
- **Documentation**: `GET /docs` (Swagger UI)

### Data Endpoints (Fetching from Database)
- **User Profile**: `GET /api/users/profile`
- **User Cards**: `GET /api/cards/`
- **User Transactions**: `GET /api/transactions/`

### Example Requests

```bash
# Health check
curl http://localhost:8000/health

# Check if database is seeded
curl http://localhost:8000/seed-status

# Get user profile from database
curl http://localhost:8000/api/users/profile

# Get user's cards from database
curl http://localhost:8000/api/cards/

# Get user's transactions from database (limit 10)
curl http://localhost:8000/api/transactions/?limit=10

# API information
curl http://localhost:8000/

# View API documentation
open http://localhost:8000/docs
```

## 🗄️ Database Configuration

### Connection Details
- **Host**: localhost
- **Port**: 5433
- **Database**: fintech_test
- **Username**: test_user
- **Password**: test_password

### Database URL
```
postgresql://test_user:test_password@localhost:5433/fintech_test
```

## 📝 Sample API Responses

### Health Check Response
```json
{
  "status": "healthy",
  "service": "fintech-api"
}
```

### Seed Status Response
```json
{
  "seeded": true,
  "user_count": 1,
  "message": "Database seeded successfully"
}
```

### API Info Response
```json
{
  "message": "FinTech API",
  "version": "1.0.0",
  "docs": "/docs",
  "status": "running",
  "database": "PostgreSQL",
  "endpoints": ["/health", "/seed-status"]
}
```

## 🛠️ Development

### Project Structure
```
services/api/
├── src/fintech/api/
│   ├── run_with_database.py    # Main server startup (routes only)
│   ├── seed_database.py        # Database seeding script
│   └── routes/                 # API route modules
│       ├── __init__.py         # Router exports
│       ├── health.py           # Health check endpoints
│       ├── users.py            # User profile endpoints
│       ├── cards.py            # Card management endpoints
│       ├── transactions.py     # Transaction endpoints
│       ├── goals.py            # Goal management endpoints
│       └── account.py          # Account summary endpoints
├── pyproject.toml              # Dependencies
├── uv.lock                     # Lock file
└── README.md                   # This file
```

### Key Features
- **Uses Existing Models Library**: Leverages fintech.models for database operations
- **Route-Based Architecture**: All endpoints defined in separate route modules
- **No Code Duplication**: API endpoints only defined in routes folder
- **Real Database Fetching**: All endpoints fetch real data from PostgreSQL
- **Proper Schemas**: Uses existing UserResponse, CardResponse, TransactionResponse
- **FastAPI**: Modern async web framework with automatic documentation
- **PostgreSQL**: Production-ready database with proper relationships
- **Clean Architecture**: Separated concerns for seeding, routes, and server startup

## 🔧 Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL container is running
docker ps | grep postgres

# Check container logs
docker logs models-postgres-test-1

# Restart database if needed
cd libs/models
docker compose -f docker-compose.test.yml restart
```

### Port Conflicts
If port 8000 is in use, modify the server startup:
```python
uvicorn.run(app, host="0.0.0.0", port=8001, reload=False)
```

### Reset Database
```bash
# Stop and remove containers
cd libs/models
docker compose -f docker-compose.test.yml down -v

# Start fresh
docker compose -f docker-compose.test.yml up -d
```

## 📚 API Documentation

Visit http://localhost:8000/docs for interactive Swagger UI documentation with:
- ✅ All endpoint details
- ✅ Request/response schemas  
- ✅ Try-it-out functionality
- ✅ Authentication info

## 🎯 Next Steps

- **Integrate Models Library**: Connect to the existing fintech.models library
- **Add Data Endpoints**: Implement user, card, and transaction endpoints
- **Add Authentication**: Implement JWT-based authentication
- **Add Business Logic**: Implement services and repositories pattern
- **Add Validation**: Comprehensive request/response validation
- **Add Testing**: Unit and integration tests
