{"$schema": "http://json-schema.org/draft-04/schema#", "title": "InsightsRequest", "description": "A request to generate insights about some candidate's fit to some vacancy", "type": "object", "properties": {"id": {"type": "string", "default": "dbfa43b5-13a9-4e0e-bd61-52cea933515c", "description": "An internal identifier that should be used to identify the generated insights. Normally, you do not need to set this value, as it will be generated automatically, depending on the API endpoint you use.", "anyOf": [{"$ref": "${ref_root}/JobCandidateInsightsId"}, {"$ref": "${ref_root}/CustomInsightsId"}]}, "candidate": {"$ref": "${ref_root}/Candidate"}, "job": {"$ref": "${ref_root}/Job"}}, "required": ["candidate", "job"]}