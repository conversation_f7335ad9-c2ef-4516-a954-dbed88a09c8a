{"$schema": "http://json-schema.org/draft-04/schema#", "title": "MediaApiFileSource", "description": "A file stored in JobCloud's Media API, associated with a valid download token", "type": "object", "properties": {"source": {"type": "string", "description": "Must be \"media_api\"", "enum": ["media_api"]}, "media_file_id": {"type": "string", "description": "Identifier of the file within Media API", "format": "uuid"}, "token": {"type": "string", "description": "A valid authentication token allowing for the file to be downloaded"}}, "required": ["source", "media_file_id", "token"]}