{"$schema": "http://json-schema.org/draft-04/schema#", "title": "ElaborationB2C1", "description": "A b2c-1 variant of elaboration, intended for seekers.", "type": "object", "default": {"requirement_fit": [{"crucial": true, "fulfilled": "fully", "content": "You are a certified trust expert with solid practical experience."}, {"crucial": false, "fulfilled": "partially", "content": "You are communicative, empathetic, and team-oriented.", "explanation": "You are communicative, empathetic, and team-oriented. However, your CV doesn't mention any experience related to it."}, {"crucial": false, "fulfilled": "no_evidence", "content": "You think entrepreneurially and have a strong customer orientation."}]}, "properties": {"requirement_fit": {"type": "array", "description": "A list of important job requirements with associated information on how well the candidate fulfills them.", "items": {"type": "object", "properties": {"crucial": {"type": "boolean", "description": "Indicates whether the requirement is crucial for the job (true) or just nice-to-have (false)."}, "fulfilled": {"type": "string", "enum": ["fully", "partially", "no_evidence"], "description": "How well does the candidate meet the requirement? Can be 'fully', 'partially' (more or less, but something is off) or 'no_evidence' (no indication in the CV)."}, "content": {"type": "string", "description": "A description of the requirement in the desired language."}, "explanation": {"type": "string", "description": "In case of a partial fit, an explanation for the seeker on why the requirement is not fully met or how to improve. Will not be present if 'fulfilled' is 'fully' or 'no_evidence'."}}, "required": ["crucial", "fulfilled", "content"]}}}}