{"$schema": "http://json-schema.org/draft-07/schema#", "title": "SourceBundle", "type": "object", "description": "Information about the static media files used to generate the insights, if any", "default": {"media_api_candidate": {"cv": {"id": "71e20325-de51-42b1-a4dc-aa4ef8353dd9"}}}, "properties": {"media_api_candidate": {"type": "object", "description": "Media files describing the candidate", "properties": {"cv": {"type": "object", "description": "The candidate's CV media file", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of the CV media file"}}, "required": ["id"]}}}}}