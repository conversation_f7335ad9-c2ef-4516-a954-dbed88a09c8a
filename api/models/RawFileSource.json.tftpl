{"$schema": "http://json-schema.org/draft-04/schema#", "title": "RawFileSource", "description": "A PDF file with explicitly provided data (Base64-encoded)", "type": "object", "properties": {"source": {"type": "string", "description": "Must be \"raw\"", "enum": ["raw"]}, "pdf_base64": {"type": "string", "description": "Base64-encoded PDF bytes", "minLength": 1, "pattern": "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"}}, "required": ["source", "pdf_base64"]}