{"$schema": "http://json-schema.org/draft-04/schema#", "title": "MarketplaceJob", "description": "A vacancy that can be fetched from the Marketplace Core API", "properties": {"source": {"type": "string", "description": "Must be \"marketplace\"", "enum": ["marketplace"]}, "vacancy_id": {"type": "string", "format": "uuid", "description": "Identifier of a vacancy within Marketplace"}}, "required": ["source", "vacancy_id"]}