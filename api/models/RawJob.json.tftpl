{"$schema": "http://json-schema.org/draft-04/schema#", "title": "<PERSON><PERSON><PERSON>", "description": "A generic, directly provided job description", "properties": {"source": {"type": "string", "description": "Must be \"raw\"", "enum": ["raw"]}, "description": {"type": "string", "description": "Textual description of a job", "minLength": 3}, "title": {"type": "string", "description": "Job title", "minLength": 3}}, "required": ["source", "description"]}