{"$schema": "http://json-schema.org/draft-04/schema#", "description": "A processing error that has prevented insights from being generated", "title": "InsightsError", "type": "object", "properties": {"id": {"$ref": "${ref_root}/InsightsId", "description": "To be removed"}, "error": {"type": "object", "description": "The error that has occurred", "properties": {"message": {"type": "string", "description": "A human-readable description of the error"}, "type": {"type": "string", "description": "Type of an exception that has caused the error"}, "code": {"type": "integer", "description": "To be removed"}}, "required": ["message"]}}, "required": ["error"]}