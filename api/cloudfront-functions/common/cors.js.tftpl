// Extracts and normalizes the request's origin.
// If not provided or provided as 'null', returns 'null' uniformly (e.g. when triggered from local files).
function normalizedOrigin(request) {
    return request.headers.origin? request.headers.origin.value : 'null';
}

// Checks whether the given request origin is allowed for CORS. Ignores the port.
function originAllowed(origin) {
    const originNoPort = origin.replace(/:\d+$/, "");
    return ${allowed_origins}.some(orig => originNoPort.endsWith(orig));
}

// Extends the given response with CORS headers.
function corsEnrichResponse(response, origin) {
    if (!response.headers) {
        response.headers = {};
    }

    Object.assign(response.headers, {
        'access-control-allow-origin': { value: origin },
        'access-control-allow-methods': { value: ${allowed_methods}.join(', ') },
        'access-control-allow-credentials': { value: 'true' },
        'access-control-allow-headers': { value: ${allowed_headers}.join(', ') },
        'access-control-max-age': { value: '${max_age}' }
    });
}

// Handles a preflight OPTIONS request, if applicable.
function handlePreflightOptions(event) {
    if (event.request.method === 'OPTIONS') {
        const origin = normalizedOrigin(event.request);
        
        if (originAllowed(origin)) {
            const response = {
                statusCode: 200,
                statusDescription: 'OK'
            };

            corsEnrichResponse(response, origin);
            return response;
        }
    
        return {
            statusCode: 403,
            statusDescription: 'Forbidden'
        };
    }
}
