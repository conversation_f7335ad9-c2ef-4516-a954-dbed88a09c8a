import crypto from 'crypto';
import cf from 'cloudfront';

const JWT_ALGO = 'HS256';
const HASH_ALGO = 'sha256';

const response401 = {
    statusCode: 401,
    statusDescription: 'Unauthorized'
};

const loggingEnabled = true;

${import.cors}

async function handler(event) {
    const preflightResponse = handlePreflightOptions(event);
    if (preflightResponse) {
        return preflightResponse;
    }

    try {
        let request = event.request;

        if (!request.headers['authorization']) {
            throw new Error('No JWT in the Authorization');
        }

        const jwtToken = request.headers['authorization'].value;
        const token = jwtToken.split('Bearer ')[1]
        const tokenDecoded = await jwtDecode(token);
        const tokenPayload = tokenDecoded.payload;
        const secret = tokenDecoded.secret;

        if (tokenPayload.exp && Date.now() > tokenPayload.exp * 1000) {
            throw new Error('Token expired');
        }

        const candidateId = tokenPayload['candidate_id'];
        if (!candidateId) {
            throw new Error(`Missing candidate_id in JWT`);
        }

        const pattern = /^(\/(?:await\/)?insights\/jobs\/)([^\/]+)\/([^\/]+)\/([^\/]+)$/
        if (!pattern.test(request.uri)) {
            throw new Error('Invalid request path');
        }

        request.uri = request.uri.replace(pattern, '$1$2/candidates/' + candidateId + '/$3/$4');
        request.headers['x-api-key'] = {value: secret};
        return request;
    } catch (e) {
        log(`Exception: $${e}`);
        return response401;
    }
}

async function jwtDecode(token) {
    const splitToken = token.split('.');
    const header = splitToken[0];
    const payload = splitToken[1];
    const signature = splitToken[2];
    if (!header || !payload || !signature) {
        throw new Error('Malformed JWT, must have 3 parts');
    }

    verifyHeader(header);

    const payloadObj = b64ToJson(payload);
    if (!payloadObj.iss) {
        throw new Error('Missing issuer in JWT');
    }
    const secret = await getSecret(payloadObj.iss);
    if (!secret) {
        throw new Error('Unrecognized issuer: ' + payloadObj.iss);
    }

    verifySignature(secret, header, payload, signature);

    return {payload: payloadObj, secret: secret};
}

function b64ToJson(blob) {
    return JSON.parse(Buffer.from(blob, 'base64').toString('utf8'));
}

function verifyHeader(header) {
    const headerObj = b64ToJson(header);
    if (headerObj.typ !== 'JWT') {
        throw new Error(`Not a JWT: $${headerObj.typ}`);
    } else if (headerObj.alg !== JWT_ALGO) {
        throw new Error(`Unsupported algorithm: $${headerObj.alg}`);
    }
}

function verifySignature(secret, header, payload, signature) {
    const expectedSignature = crypto.createHmac(HASH_ALGO, secret)
        .update(`$${header}.$${payload}`)
        .digest('base64url');

    if (signature !== expectedSignature) {
        throw new Error('Invalid signature');
    }
}

async function getSecret(key) {
    try {
        const kvsHandle = cf.kvs();
        return await kvsHandle.get(key);
    } catch (err) {
        throw new Error(`Error reading value for key: $${key}, error: $${err}`);
    }
}

function log(message) {
    if (loggingEnabled) {
        console.log(message);
    }
}
