#** Terraform arguments:
    - max_depth (the maximum depth of recursion to allow)
*#

#** Recursively shows a DynamoDB value (object, list or primitive) as JSON.
    Arguments:
    - $value (the value to show)
    - $exclude (a list of keys to exclude from the output)

    Supported types:
    - String (S)
    - Number (N)
    - Boolean (BOOL)
    - Map (M)
    - List (L)
    
    If some value's type is unsupported, it will be shown as null.

    Note: Due to Apache VTL's limit on #define recursions, this snippet is
          generated multiple times, once for each level of recursion.
          Also, because AWS API Gateway does not allow actual macros (functions),
          this needs to hack around shadowing by using a list as a stack,
          with the first (top) element being the actual value to render
          at the current depth, removed after the sub-call is finished.
*#
#define( $showDynamo )
#set($values=[$value])
$showValue0
#end

%{ for i in range(max_depth) ~}
#define( $showValue${i} )
  #if($values.get(0).S != "")
    "$values.get(0).S"
  #elseif($values.get(0).N != "")
    $values.get(0).N
  #elseif($values.get(0).BOOL != "")
    $values.get(0).BOOL
  #elseif($values.get(0).M != "")
    {
      #foreach($key in $values.get(0).M.keySet())
        #if(!$exclude.contains($key))
            #set($dummy=$values.add(0, $values.get(0).M.get($key)))
            "$key":
              $showValue${i+1}
            #if($foreach.hasNext())
              ,
            #end
            #set($dummy=$values.remove(0))
        #end
      #end
    }
  #elseif($values.get(0).L != "")
    [
        #foreach($item in $values.get(0).L)
            #set($dummy=$values.add(0, $item))
            $showValue${i+1}
            #if($foreach.hasNext())
              ,
            #end
            #set($dummy=$values.remove(0))
        #end
    ]
  #else
    #[[null]]#
  #end
#end
%{ endfor ~}
