#** Shows the insights object as <PERSON><PERSON><PERSON>, or an error if one has occurred.
    In case of an error, the status code will be modified accordingly.
    Arguments:
    - $insights (the insights object to show)
*#

${import.show_dynamo}

#define( $showInsights )
#if($insights == "")
  #set($context.responseOverride.status = 404)
  {
    "error":{
      "message":"Insights do not exist!",
      "type":"NOT_FOUND",
      "code":404
    }
  }
#elseif($insights.error.M != "")
  #set($context.responseOverride.status = 422)
  #set($err=$insights.error.M)
  {
    "id":"$insights.id.S",
    "variant":"$insights.variant.S",
    "lang":"$insights.lang.S",
    "error":{
      "message":"An error has prevented insights from being generated!",
      "type":"$err.type.S"
    }
  }
#else
  #set($elab=$insights.elaboration.M)
  {
    "id":"$insights.id.S",
    "variant":"$insights.variant.S",
    "lang":"$insights.lang.S",
    "bundle":
        #set($exclude=[])
        #set($value=$insights.bundle)
        $showDynamo ,
    "elaboration":
        #set($exclude=["metadata"])
        #set($value=$insights.elaboration)
        $showDynamo
  }
#end
#end
