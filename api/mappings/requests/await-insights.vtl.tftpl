#if($input.path('$.id') != "")
  #set($id=$input.path('$.id'))
#else
  #set($id=$context.requestId)
#end
#if($input.params('force') == "")
  #set($force=false)
#else
  #set($force=$input.params('force'))
#end
{
  "input": "{\"id\": \"$id\", \"job\": $util.escapeJavaScript($input.json('$.job')), \"candidate\": $util.escapeJavaScript($input.json('$.candidate')), \"variants\": [\"$util.escapeJavaScript($input.params('variant'))\"], \"languages\": [\"$util.escapeJavaScript($input.params('lang'))\"], \"force\": $force  }",
  "name": "$context.requestId",
  "stateMachineArn": "$util.escapeJavaScript($stageVariables.awaiterFuncArn)"
}