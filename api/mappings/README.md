# Apache VTL mappings

AWS API Gateway uses Apache VTL as a template language for request and response transformations.
It's an ancient Java-based technology with a lot of limitations, so we have to resort to a few workarounds
and conventions in order to make it usable.

## Structure
Every mapping is defined in a separate file as a Terraform template and might pull in definitions from
the `stdlib` directory for common patterns. When rendering the templates, we provide an `import` object
as part of the template context. In order to include some definitions, just refer to an stdlib module
by name at the beginning of the VTL code:
```hcl
${import.show_dynamo}
```

## Stdlib
The common functionality is defined using VTL's `#define` directives. Normally, an actual macro would
be the proper tool for the job, but API Gateway has them explicitly disabled. You can think about
`#define`s as just pasting the same piece of code every time it's referenced. They also don't support
arguments (because they are not functions), but rather refer to some variables that should already exist.

In order to "call" such a "function", you need to first define the arguments it requires using `#set`
and then just refer to it by name, e.g.:
```vtl
#set($value = {"M": {"someKey": {"S": "someValue"}}})
#set($exclude = ["skippedKey"])
$showDynamo
```

All the arguments NEED to be specified.

### Caveat 1: No scoping and shadowing
Given that there are no actual arguments, but rather variables already existing in global scope
with some predefined names, if you want to recurse, you need to make sure that you don't overwrite
the original variable at any point, but still use the same variable name for the nested calls.

The way we hacked around this limitation here is by using a list (simulating a stack) to pass
the arguments to the nested calls. The first element of the list is the actual value, while
the rest are just previous values used at the previous recursion levels. When performing the
recursive call, instead of changing the argument value, we just prepend a new item to the stack.
All the recursive functions are defined to just use the first element of the list as the value.
After the call is finished, we pop the item from the stack.

So, instead of:
```vtl
#set($value = "Something")
Deal with $value

#set($value = "Something else")
Keep working with $value
```

we end up with:
```vtl
#set($values = ["Something"])
Deal with $values.get(0)

#set($dummy = $values.add("Something else"))
Keep working with $values.get(0)
#set($dummy = $values.remove(0))
```

### Caveat 2: Recursion limit for #defines
Apache VTL doesn't allow `#define` to recurse more than once. If you try to do this, the second
reference to the definition will just not be interpolated. In order to work around this limitation,
we generate the same definition multiple times, with consecutive numbers appended to the name.
When doing the recursive call, we just refer to the definition with the consecutive number appended
to the name. This circumvents the VTL's limit (at the cost of code size). The desired max depth
needs to be known upfront, so that we can generate the proper number of definitions.

### Caveat 3: VTL preserves all the whitespace
Every indentation and newline you make in your "code" is retained in the generated output.
However, VTL is itself almost completely insensitive to whitespace. In order to avoid returning rubbish
whitespace as part of the generated JSON string, while still retaining readability of the code,
we just **remove all the leading and trailing whitespace on a line** when applying the templates
using Terraform. We don't remove the whitespace in the middle of the line, so for minimal JSON,
you should explicitly avoid putting spaces around the colons and commas.

When stripping whitespace, there is also one special case that you need to be aware of:
if a naked directive (e.g. `#else`) is directly followed by a letter, they would be merged,
making a wrong directive (e.g. `#elsefoo`). In order to avoid this, you can use a `#[[foo]]#`
(unparsed content) syntax to wrap the literal. `#[[foo]]#` is equivalent to `foo`, but safe.

## Where to learn from?
* [Apache VTL documentation](https://velocity.apache.org/engine/2.0/vtl-reference.html)
* [AWS API Gateway documentation](https://docs.aws.amazon.com/apigateway/latest/developerguide/models-mappings.html)
* [A blog post with a comprehensive example from some guy](https://community.aws/content/2f0C1U6QRHgVMRaN4L8dhMur3EB/using-api-gateway-mapping-templates-for-direct-dynamodb-integrations?lang=en)