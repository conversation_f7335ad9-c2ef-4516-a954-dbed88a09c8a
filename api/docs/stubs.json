{
    "openapi": "3.0.1",
    "info": {
        "title": "TEST",
        "description": "## TESTS",
        "contact": {
            "name": "Problems or questions?",
        }
    },
    "components": {
        "securitySchemes": {
            "api_key": {
                "description": "API key-based authentication for machine-to-machine communication"
            }
        }
    },
    "tags": [
        {
            "name": "default",
            "description": "Unrestricted machine-to-machine API"
        },
        {
            "name": "jwt",
            "description": "Restricted JWT-based API"
        }
    ]
}
