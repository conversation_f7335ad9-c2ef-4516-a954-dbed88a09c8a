# ### Settings:
locals {
  access_keys = {
    fintech-demo = { plan = aws_api_gateway_usage_plan.demo.id, enabled = true }
  }
}

# ### Usage plans:
resource "aws_api_gateway_usage_plan" "demo" {
  name        = "fintech-demo"
  description = "Usage plan for the Fintech API for demo purposes"

  api_stages {
    api_id = aws_api_gateway_rest_api.fintech.id
    stage  = aws_api_gateway_stage.fintech.stage_name
  }

  quota_settings {
    limit  = 1000
    period = "DAY"
  }

  throttle_settings {
    burst_limit = 15
    rate_limit  = 3
  }
}

resource "aws_api_gateway_usage_plan" "seeker_browser" {
  name        = "seeker-browser"
  description = "Usage plan for the fintech API when used publicly from Seekers' browsers"

  api_stages {
    api_id = aws_api_gateway_rest_api.fintech.id
    stage  = aws_api_gateway_stage.fintech.stage_name
  }

  throttle_settings {
    burst_limit = 100
    rate_limit  = 10
  }
}

# ### API keys/JWT secrets:
resource "aws_api_gateway_api_key" "api_key" {
  for_each = local.access_keys
  name     = each.key
  enabled  = each.value.enabled

  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_api_gateway_usage_plan_key" "api_key_association" {
  for_each      = local.access_keys
  key_id        = aws_api_gateway_api_key.api_key[each.key].id
  key_type      = "API_KEY"
  usage_plan_id = each.value.plan
}

resource "terraform_data" "jwt_secret" {
  for_each = local.access_keys
  input = {
    name  = aws_api_gateway_api_key.api_key[each.key].name
    store = aws_cloudfront_key_value_store.secrets.arn
    env   = local.env
  }
  triggers_replace = [
    aws_cloudfront_key_value_store.secrets.arn,
    aws_api_gateway_api_key.api_key[each.key].name,
    aws_api_gateway_api_key.api_key[each.key].value,
    aws_api_gateway_api_key.api_key[each.key].enabled
  ]

  provisioner "local-exec" {
    when    = create
    command = <<-EOF
      if [ "${aws_api_gateway_api_key.api_key[each.key].enabled}" == "true" ]; then
        just cf-set-secret ${self.output.env} ${self.output.store} ${self.output.name} ${aws_api_gateway_api_key.api_key[each.key].value};
      else
        just cf-delete-secret ${self.output.env} ${self.output.store} ${self.output.name};
      fi
    EOF
  }

  provisioner "local-exec" {
    when    = destroy
    command = "just cf-delete-secret ${self.output.env} ${self.output.store} ${self.output.name}"
  }
}
