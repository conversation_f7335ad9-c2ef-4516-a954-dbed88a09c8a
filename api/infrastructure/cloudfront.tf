locals {
  api_origin_id    = "fintech-apigw"
  assets_origin_id = "fintech-assets-s3"
}

resource "aws_cloudfront_distribution" "fintech_api" {
  enabled         = true
  aliases         = [local.api_domain_name]
  comment         = "Public API (${local.env})"
  is_ipv6_enabled = true
  http_version    = "http2and3"
  price_class     = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "whitelist"
      locations        = local.settings.countries_allowed
    }
  }

  viewer_certificate {
    acm_certificate_arn      = aws_acm_certificate.fintech_api_cert.arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }

  origin {
    origin_id   = local.assets_origin_id
    domain_name = aws_s3_bucket_website_configuration.fintech_api_assets.website_endpoint

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "http-only"
      origin_ssl_protocols   = ["SSLv3", "TLSv1", "TLSv1.1", "TLSv1.2"]
    }
  }

  origin {
    origin_id   = local.api_origin_id
    domain_name = "${aws_api_gateway_rest_api.fintech.id}.execute-api.${data.aws_region.current.name}.amazonaws.com"
    origin_path = "/${local.env}"

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }

  default_cache_behavior {
    target_origin_id       = local.api_origin_id
    viewer_protocol_policy = "https-only"
    allowed_methods        = ["GET", "HEAD", "OPTIONS", "PUT", "POST", "PATCH", "DELETE"]
    cached_methods         = ["GET", "HEAD", "OPTIONS"]

    function_association {
      event_type   = "viewer-request"
      function_arn = aws_cloudfront_function.preflight_handler.arn
    }

    function_association {
      event_type   = "viewer-response"
      function_arn = aws_cloudfront_function.cors_enricher.arn
    }

    cache_policy_id          = data.aws_cloudfront_cache_policy.disabled.id
    origin_request_policy_id = data.aws_cloudfront_origin_request_policy.all_except_host.id
  }

  ordered_cache_behavior {
    path_pattern           = "/demo*"
    target_origin_id       = local.assets_origin_id
    viewer_protocol_policy = "redirect-to-https"
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    compress               = true

    cache_policy_id = aws_cloudfront_cache_policy.short_lived.id
  }

  ordered_cache_behavior {
    path_pattern           = "/docs*"
    target_origin_id       = local.assets_origin_id
    viewer_protocol_policy = "redirect-to-https"
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    compress               = true

    cache_policy_id = aws_cloudfront_cache_policy.short_lived.id
  }
}

resource "aws_cloudfront_cache_policy" "short_lived" {
  name = "ShortLivedCache"

  default_ttl = 60
  max_ttl     = 300
  min_ttl     = 1
  comment     = "Caching policy intended for frequently changing static assets, cached for up to 5 minutes"

  parameters_in_cache_key_and_forwarded_to_origin {
    headers_config {
      header_behavior = "none"
    }

    query_strings_config {
      query_string_behavior = "none"
    }

    cookies_config {
      cookie_behavior = "none"
    }

    enable_accept_encoding_brotli = true
    enable_accept_encoding_gzip   = true
  }
}

locals {
  cf_common_cors = templatefile("${path.module}/../cloudfront-functions/common/cors.js.tftpl", {
    allowed_origins = jsonencode(local.settings.cors.allowed_origins)
    allowed_methods = jsonencode(local.settings.cors.allowed_methods)
    allowed_headers = jsonencode(local.settings.cors.allowed_headers)
    max_age         = jsonencode(local.settings.cors.max_age)
  })

  cf_jwt_validator = templatefile("${path.module}/../cloudfront-functions/jwt-validator.js.tftpl", {
    import = { cors = local.cf_common_cors }
  })

  cf_preflight_handler = templatefile("${path.module}/../cloudfront-functions/preflight-handler.js.tftpl", {
    import = { cors = local.cf_common_cors }
  })

  cf_cors_enricher = templatefile("${path.module}/../cloudfront-functions/cors-enricher.js.tftpl", {
    import = { cors = local.cf_common_cors }
  })
}

resource "aws_cloudfront_function" "jwt_validator" {
  name                         = "${local.project.name}-${local.project.env}-jwt-validator"
  runtime                      = "cloudfront-js-2.0"
  publish                      = true
  code                         = local.cf_jwt_validator
  key_value_store_associations = [aws_cloudfront_key_value_store.secrets.arn]
}

resource "aws_cloudfront_function" "preflight_handler" {
  name    = "${local.project.name}-${local.project.env}-preflight-handler"
  runtime = "cloudfront-js-2.0"
  publish = true
  code    = local.cf_preflight_handler
}

resource "aws_cloudfront_function" "cors_enricher" {
  name    = "${local.project.name}-${local.project.env}-cors-enricher"
  runtime = "cloudfront-js-2.0"
  publish = true
  code    = local.cf_cors_enricher
}

resource "aws_cloudfront_key_value_store" "secrets" {
  name    = "${local.project.name}-${local.project.env}-await-insights-jwt-validator-secrets"
  comment = "Secrets for CloudFront validator function"
}

data "aws_cloudfront_cache_policy" "disabled" {
  name = "Managed-CachingDisabled"
}

data "aws_cloudfront_cache_policy" "optimized" {
  name = "Managed-CachingOptimized"
}

data "aws_cloudfront_origin_request_policy" "all_except_host" {
  name = "Managed-AllViewerExceptHostHeader"
}

data "aws_cloudfront_response_headers_policy" "simple_cors" {
  name = "Managed-SimpleCORS"
}
