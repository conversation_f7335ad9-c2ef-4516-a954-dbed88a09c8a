locals {
  # { ModelName => { SchemaObject }, ... }
  models = {
    for fname in fileset("${local.root}/models", "*.json.tftpl") :
    replace(fname, ".json.tftpl", "") =>
    jsondecode(
      templatefile(
        "${local.root}/models/${fname}",
        {
          ref_root = "https://apigateway.amazonaws.com/restapis/${aws_api_gateway_rest_api.fintech.id}/models"
        }
      )
    )
  }
}

resource "aws_api_gateway_model" "model" {
  for_each = local.models

  rest_api_id  = aws_api_gateway_rest_api.fintech.id
  name         = each.value.title
  description  = each.value.description
  content_type = "application/json"
  schema       = jsonencode(each.value)
}
