terraform {
  backend "s3" {
    bucket               = "fintech-tf-states"
    workspace_key_prefix = "fintech-api"
    key                  = "state.tfstate"
    region               = "eu-central-1"
    encrypt              = true
    kms_key_id           = "" #todo
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.75.1"
    }
  }

  required_version = ">= 1.5.2"
}

provider "aws" {
  region = "eu-central-1"
  assume_role {
    role_arn = local.settings.role_arn
  }
}

provider "aws" {
  alias  = "us"
  region = "us-east-1"
  assume_role {
    role_arn = local.settings.role_arn
  }
}

locals {
  env  = terraform.workspace
  root = "${path.module}/.."

  project = {
    name = "fintech-api"
    env  = local.env
  }

  project_name = "${local.project.name}-${local.project.env}"
  eu_countries = [
    "AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR",
    "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL",
    "PL", "PT", "RO", "SK", "SI", "ES", "SE"
  ]

  settings = {
    role_arn          = var.workspace_settings[local.env].role_arn
    sensitive_logging = var.workspace_settings[local.env].sensitive_logging
    countries_allowed = concat(local.eu_countries, var.workspace_settings[local.env].non_eu_countries_allowed)
    cors              = var.workspace_settings[local.env].cors
  }
}

variable "workspace_settings" {
  type = map(object({
    role_arn                 = string
    non_eu_countries_allowed = list(string)
    sensitive_logging        = bool
    cors                     = object({
      allowed_origins = list(string)
      allowed_methods = list(string)
      allowed_headers = list(string)
      max_age         = number
    })
  }))

  default = {
    dev = {
      role_arn                 = "arn:aws:iam::407276168849:role/tf-infra-dev-deploy-role"
      await_timeout            = 60
      non_eu_countries_allowed = ["CH", "LI", "RS"]
      sensitive_logging        = true
      cors = {
        allowed_origins = ["", "localhost", "null"]
        allowed_methods = ["GET", "POST", "OPTIONS"]
        allowed_headers = ["authorization", "content-type", "x-api-key", "x-source", "x-node-request", "x-request-id"]
        max_age         = 30
      }
    }

    prod = {
      role_arn                 = "arn:aws:iam::304848207041:role/tf-infra-prod-deploy-role"
      await_timeout            = 60
      non_eu_countries_allowed = ["CH", "LI"]
      sensitive_logging        = false
      cors = {
        allowed_origins = [""]
        allowed_methods = ["GET", "POST", "OPTIONS"]
        allowed_headers = ["authorization", "content-type", "x-api-key", "x-source", "x-node-request", "x-request-id"]
        max_age         = 300
      }
    }
  }
}
