resource "aws_route53_zone" "fintech" {
  name = "${local.env}.todo.domain.com" #todo add domain
}

locals {
  api_domain_name = "api.${aws_route53_zone.fintech.name}"
}

resource "aws_acm_certificate" "fintech_api_cert" {
  provider                  = aws.us
  domain_name               = aws_route53_zone.fintech.name
  subject_alternative_names = ["*.${aws_route53_zone.fintech.name}"]
  validation_method         = "DNS"
}

resource "aws_route53_record" "fintech_api_cert_validation" {
  for_each = {
    for dvo in aws_acm_certificate.fintech_api_cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      type   = dvo.resource_record_type
      record = dvo.resource_record_value
    }
  }

  zone_id = aws_route53_zone.fintech.zone_id
  name    = each.value.name
  type    = each.value.type
  ttl     = 60
  records = [each.value.record]
}

resource "aws_acm_certificate_validation" "fintech_api_cert_validation" {
  provider                = aws.us
  certificate_arn         = aws_acm_certificate.fintech_api_cert.arn
  validation_record_fqdns = [for record in aws_route53_record.fintech_api_cert_validation : record.fqdn]
}

resource "aws_route53_record" "fintech_api_alias" {
  for_each = toset(["A", "AAAA"])

  zone_id = aws_route53_zone.fintech.zone_id
  name    = local.api_domain_name
  type    = each.value

  alias {
    name                   = aws_cloudfront_distribution.fintech_api.domain_name
    zone_id                = aws_cloudfront_distribution.fintech_api.hosted_zone_id
    evaluate_target_health = false
  }
}
