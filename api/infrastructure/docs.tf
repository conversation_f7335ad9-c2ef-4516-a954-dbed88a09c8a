

resource "aws_api_gateway_documentation_part" "response_200" {
  rest_api_id = aws_api_gateway_rest_api.fintech.id
  location {
    type        = "RESPONSE"
    status_code = 200
    method      = "*"
    path        = "/"
  }

  properties = jsonencode({
    description = "200 OK response"
  })
}


resource "aws_api_gateway_documentation_part" "method_get_insights_applications" {
  rest_api_id = aws_api_gateway_rest_api.fintech.id
  location {
    type   = "METHOD"
    path   = aws_api_gateway_resource.test_id.path
    method = "GET"
  }

  properties = jsonencode({
    description = "Test"
    summary     = "Test"
  })
}

