resource "aws_api_gateway_rest_api" "fintech" {
  name = "fintech"
}

resource "aws_api_gateway_deployment" "fintech" {
  rest_api_id = aws_api_gateway_rest_api.fintech.id
}

resource "aws_api_gateway_stage" "fintech" {
  rest_api_id   = aws_api_gateway_rest_api.fintech.id
  deployment_id = aws_api_gateway_deployment.fintech.id
  stage_name    = local.env


  lifecycle {
    ignore_changes = [deployment_id, documentation_version]
  }
}

resource "aws_api_gateway_request_validator" "body" {
  rest_api_id                 = aws_api_gateway_rest_api.fintech.id
  name                        = "Validate body"
  validate_request_body       = true
  validate_request_parameters = false
}
