### Permissions: API Gateway -> Step Functions
resource "aws_iam_role" "api_gateway_to_step_functions" {
  name = "${local.project.name}-${local.project.env}-api-gateway-to-step-functions-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

### Route and integrations:
resource "aws_api_gateway_resource" "test" {
  rest_api_id = aws_api_gateway_rest_api.fintech.id
  parent_id   = aws_api_gateway_rest_api.fintech.root_resource_id
  path_part   = "test"
}

resource "aws_api_gateway_resource" "test_id" {
  rest_api_id = aws_api_gateway_rest_api.fintech.id
  parent_id   = aws_api_gateway_resource.test.id
  path_part   = "id"
}


# POST /await/insights/{variant}/{lang}
resource "aws_api_gateway_method" "post_await_insights_variant_lang" {
  rest_api_id      = aws_api_gateway_rest_api.fintech.id
  resource_id      = aws_api_gateway_resource.test_id.id
  http_method      = "POST"
  authorization    = "NONE"
  api_key_required = true
  request_parameters = {
    "method.request.path.lang"         = true
    "method.request.path.variant"      = true
    "method.request.querystring.force" = false
  }
  request_models = {
    "application/json" = aws_api_gateway_model.model["Test"].name
  }
  request_validator_id = aws_api_gateway_request_validator.body.id
}

resource "aws_api_gateway_integration" "post_await_insights_variant_lang" {
  rest_api_id          = aws_api_gateway_rest_api.fintech.id
  resource_id          = aws_api_gateway_resource.test_id.id
  http_method          = aws_api_gateway_method.post_await_insights_variant_lang.http_method
  timeout_milliseconds = 45000

  type                    = "AWS"
  integration_http_method = "POST"
  request_templates = ""

  passthrough_behavior = "WHEN_NO_TEMPLATES"
  content_handling     = "CONVERT_TO_TEXT"
}

resource "aws_api_gateway_method_response" "test" {
  rest_api_id = aws_api_gateway_rest_api.fintech.id
  resource_id = aws_api_gateway_resource.test_id.id
  http_method = aws_api_gateway_method.post_await_insights_variant_lang.http_method
  status_code = aws_api_gateway_integration_response.test.status_code
}

resource "aws_api_gateway_integration_response" "test" {
  rest_api_id      = aws_api_gateway_rest_api.fintech.id
  resource_id      = aws_api_gateway_resource.test_id.id
  http_method      = aws_api_gateway_method.post_await_insights_variant_lang.http_method
  status_code      = 200
  content_handling = "CONVERT_TO_TEXT"
  response_templates = {
    "application/json" = local.mappings["responses"]["await-insights"]
  }
}
