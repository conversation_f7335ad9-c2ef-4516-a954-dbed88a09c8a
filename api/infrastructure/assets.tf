resource "aws_s3_bucket" "fintech_api_assets" {
  bucket = "fintech-api-assets-${local.env}"
}

resource "aws_s3_bucket_policy" "fintech_api_assets" {
  bucket = aws_s3_bucket.fintech_api_assets.bucket
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid       = "PublicReadAccess",
        Effect    = "Allow",
        Principal = "*",
        Action    = "s3:GetObject",
        Resource  = "${aws_s3_bucket.fintech_api_assets.arn}/*"
      }
    ]
  })
}

resource "aws_s3_bucket_public_access_block" "fintech_api_assets" {
  bucket = aws_s3_bucket.fintech_api_assets.bucket

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = false
}

resource "aws_s3_bucket_website_configuration" "jobfit_api_assets" {
  bucket = aws_s3_bucket.fintech_api_assets.bucket

  index_document {
    suffix = "index.html"
  }
}
