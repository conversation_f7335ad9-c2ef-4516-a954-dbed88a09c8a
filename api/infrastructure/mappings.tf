locals {
  vtl_show_dynamo = templatefile("${local.root}/mappings/stdlib/show-dynamo.vtl.tftpl", {
    max_depth = 4
  })
  vtl_show_insights = templatefile("${local.root}/mappings/stdlib/show-insights.vtl.tftpl", {
    import = { show_dynamo = local.vtl_show_dynamo }
  })

  # { requests => mapping-name => rendered VTL, ... }
  mappings = {
    for kind in ["requests", "responses"] :
    kind => {
      for fname in fileset("${local.root}/mappings/${kind}", "*.vtl.tftpl") :
      replace(fname, ".vtl.tftpl", "") =>
      join(
        "",
        [
          for line in split("\n", templatefile(
            "${local.root}/mappings/${kind}/${fname}",
            {
              import = {
                show_dynamo   = local.vtl_show_dynamo,
                show_insights = local.vtl_show_insights
              }
            }
          )) :
          trimspace(line) # Remove all the leading and trailing whitespace, line-wise
        ]
      )
    }
  }
}
