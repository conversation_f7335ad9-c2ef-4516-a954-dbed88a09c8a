set allow-duplicate-variables := true

tf-dir := './infrastructure'

import '../just/assets.just'
import '../just/cloudfront.just'
import '../just/openapi.just'
import '../just/terraform.just'
import '../just/versioning.just'

# Deploy the API, including infrastructure and apps to <env>.
deploy env version=auto-version: (tf-deploy env) (deploy-demo env) (deploy-docs env)

# Deploy the API demo web app to <env>.
deploy-demo env: (assets-deploy env 'demo' 'demo')

# Generate and deploy the API docs to <env>.
deploy-docs env version=auto-version dir=shell('mktemp -d'): \
    (generate-docs env version dir) \
    && (assets-deploy env dir 'docs')
    @cp ./docs/index.html {{dir}}

# Generate the OpenAPI spec for <env> and <version>.
generate-docs env version=auto-version dir=shell('mktemp -d'): \
    (openapi-import-apigw env 'jobfit' join(dir, 'api-spec.json')) \
    (openapi-merge join(dir, 'api-spec.json') './docs/cloudfront-api.json' './docs/stubs.json') \
    (openapi-modify join(dir, 'api-spec.json') \
        '.servers = [{
            url: "https://api.\($ENV_NAME).jobfit.jobcloud.services",
            description: "JobFit API \($ENV_NAME) environment"
        }] |
        .info.version = $VERSION |
        del(.paths.[].[].parameters.[] | select(.name == "x-api-key"))' \
        prepend('ENV_NAME=', env) \
        prepend('VERSION=', version))
    @echo "OpenAPI docs generated in: {{dir}}/api-spec.json"
